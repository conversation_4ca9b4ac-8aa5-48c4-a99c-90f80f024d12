# models/encoder.py
import torch
import torch.nn as nn
from timm.models.layers import trunc_normal_

# Import shared components from .modules
from .modules import PatchEmbed, PatchMerging, SwinTransformerBlock


class BasicLayerEncoder(nn.Module):
    """A basic Swin Transformer layer for one stage in the encoder."""

    def __init__(
        self,
        dim,
        out_dim,
        input_resolution,
        depth,
        num_heads,
        window_size,
        mlp_ratio=4.0,
        qkv_bias=True,
        qk_scale=None,
        norm_layer=nn.LayerNorm,
        downsample_layer_type=None,
        drop_path_rates=None,
    ):
        super().__init__()
        self.dim = dim
        self.out_dim = out_dim
        self.input_resolution = input_resolution
        self.depth = depth
        self.drop_path_rates = (
            drop_path_rates if drop_path_rates is not None else [0.0] * depth
        )

        if downsample_layer_type is not None:
            if input_resolution[0] < 2 or input_resolution[1] < 2:
                self.downsample = None
                self.block_input_resolution = input_resolution
                self.projection = (
                    nn.Linear(dim, out_dim) if dim != out_dim else nn.Identity()
                )
                self.block_input_dim = out_dim
            else:
                self.downsample = downsample_layer_type(
                    input_resolution, dim=dim, out_dim=out_dim, norm_layer=norm_layer
                )
                self.block_input_resolution = (
                    input_resolution[0] // 2,
                    input_resolution[1] // 2,
                )
                self.block_input_dim = out_dim
                self.projection = nn.Identity()
        else:
            self.downsample = None
            self.block_input_resolution = input_resolution
            self.projection = (
                nn.Linear(dim, out_dim) if dim != out_dim else nn.Identity()
            )
            self.block_input_dim = out_dim

        self.blocks = nn.ModuleList(
            [
                SwinTransformerBlock(
                    dim=self.block_input_dim,
                    input_resolution=self.block_input_resolution,
                    num_heads=num_heads,
                    window_size=window_size,
                    shift_size=0 if (i % 2 == 0) else window_size // 2,
                    mlp_ratio=mlp_ratio,
                    qkv_bias=qkv_bias,
                    qk_scale=qk_scale,
                    norm_layer=norm_layer,
                    drop_path=self.drop_path_rates[i],
                )
                for i in range(depth)
            ]
        )

        for i, blk in enumerate(self.blocks):
            if hasattr(blk, "_set_original_window_shift_size"):
                blk._set_original_window_shift_size(
                    window_size, 0 if (i % 2 == 0) else window_size // 2
                )

    def forward(self, x):
        if self.downsample is not None:
            x_processed = self.downsample(x)
        else:
            x_processed = self.projection(x)

        for blk in self.blocks:
            x_processed = blk(x_processed)
        return x_processed

    def update_resolution(self, H_new, W_new):
        self.input_resolution = (H_new, W_new)

        if self.downsample is not None:
            if H_new < 2 or W_new < 2:
                # If downsampler was configured but input is now too small,
                # the downsampler itself (PatchMerging) has a passthrough.
                # The resolution for blocks will be H_new, W_new.
                # The projection in BasicLayerEncoder handles dim change if needed.
                self.downsample.input_resolution = (H_new, W_new)
                res_for_blocks_H = H_new
                res_for_blocks_W = W_new
                # Block input dim should be self.out_dim (target after projection in PatchMerging or here)
                # This part of logic needs to be consistent with PatchMerging's passthrough.
                # If PatchMerging passes through, its output dim is self.out_dim (if projected) or self.dim.
                # BasicLayerEncoder's self.projection handles the dim change from self.dim to self.out_dim if no downsample.
                # If downsample IS present but passes through, its output is out_dim.
                # So, block_input_dim remains self.out_dim.
            else:
                self.downsample.input_resolution = (H_new, W_new)
                res_for_blocks_H = H_new // 2
                res_for_blocks_W = W_new // 2
        else:  # No downsampler module
            res_for_blocks_H = H_new
            res_for_blocks_W = W_new

        self.block_input_resolution = (res_for_blocks_H, res_for_blocks_W)
        for blk in self.blocks:
            blk.update_resolution(res_for_blocks_H, res_for_blocks_W)


class WITT_Encoder(nn.Module):
    def __init__(
        self,
        img_size=(64, 64),
        patch_size=2,
        in_chans=3,
        embed_dims=(128, 256, 512),
        depths=(2, 4, 4),
        num_heads=(2, 4, 8),
        C=96,
        window_size=2,
        mlp_ratio=4.0,
        qkv_bias=True,
        qk_scale=None,
        norm_layer=nn.LayerNorm,
        patch_norm=True,
        stochastic_depth_prob=0.1,
        use_adaptive_modulation=False,
    ):
        super().__init__()
        self.num_stages = len(depths)
        self.patch_norm = patch_norm
        self.embed_dims = embed_dims
        self.final_embed_dim = embed_dims[-1]
        self.use_adaptive_modulation = use_adaptive_modulation
        self.patch_size_val = patch_size

        self.patch_embed = PatchEmbed(
            img_size,
            patch_size,
            in_chans,
            embed_dims[0],
            norm_layer=norm_layer if self.patch_norm else None,
        )

        patches_resolution = self.patch_embed.patches_resolution

        dpr = [x.item() for x in torch.linspace(0, stochastic_depth_prob, sum(depths))]

        self.layers = nn.ModuleList()
        current_input_dim_for_stage = embed_dims[0]
        current_resolution_for_stage = patches_resolution
        dpr_offset = 0

        for i_stage in range(self.num_stages):
            stage_out_dim = embed_dims[i_stage]

            layer = BasicLayerEncoder(
                dim=current_input_dim_for_stage,
                out_dim=stage_out_dim,
                input_resolution=current_resolution_for_stage,
                depth=depths[i_stage],
                num_heads=num_heads[i_stage],
                window_size=window_size,
                mlp_ratio=mlp_ratio,
                qkv_bias=qkv_bias,
                qk_scale=qk_scale,
                norm_layer=norm_layer,
                downsample_layer_type=PatchMerging
                if (i_stage < self.num_stages - 1)
                else None,
                drop_path_rates=dpr[dpr_offset : dpr_offset + depths[i_stage]],
            )
            self.layers.append(layer)

            current_input_dim_for_stage = stage_out_dim
            if i_stage < self.num_stages - 1:
                if (
                    current_resolution_for_stage[0] >= 2
                    and current_resolution_for_stage[1] >= 2
                ):
                    current_resolution_for_stage = (
                        current_resolution_for_stage[0] // 2,
                        current_resolution_for_stage[1] // 2,
                    )

            dpr_offset += depths[i_stage]

        self.norm_final = norm_layer(self.final_embed_dim)

        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            trunc_normal_(m.weight, std=0.02)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)

    def forward(self, x_img):
        x = self.patch_embed(x_img)

        for layer in self.layers:
            x = layer(x)

        x = self.norm_final(x)
        return x

    def update_resolution(self, H_new, W_new):
        self.patch_embed.img_size_config = (H_new, W_new)
        self.patch_embed._update_patches_resolution(H_new, W_new)

        current_resolution = self.patch_embed.patches_resolution
        for i_stage, layer in enumerate(self.layers):
            layer.update_resolution(current_resolution[0], current_resolution[1])
            if i_stage < self.num_stages - 1:
                if (
                    layer.downsample is not None
                    and current_resolution[0] >= 2
                    and current_resolution[1] >= 2
                ):
                    current_resolution = (
                        current_resolution[0] // 2,
                        current_resolution[1] // 2,
                    )


def create_encoder(**kwargs):
    expected_args = [
        "img_size",
        "patch_size",
        "in_chans",
        "embed_dims",
        "depths",
        "num_heads",
        "C",
        "window_size",
        "mlp_ratio",
        "qkv_bias",
        "qk_scale",
        "norm_layer",
        "patch_norm",
        "stochastic_depth_prob",
        "use_adaptive_modulation",
    ]

    encoder_specific_kwargs = {k: v for k, v in kwargs.items() if k in expected_args}

    if "stochastic_depth_prob" not in encoder_specific_kwargs:
        encoder_specific_kwargs["stochastic_depth_prob"] = 0.1
    if "use_adaptive_modulation" not in encoder_specific_kwargs:
        encoder_specific_kwargs["use_adaptive_modulation"] = False

    model = WITT_Encoder(**encoder_specific_kwargs)
    return model
