# models/quantization.py
import torch
import torch.nn as nn


class LBSign(torch.autograd.Function):
    """
    Straight-Through Estimator for Sign function.
    """

    @staticmethod
    def forward(ctx, input_tensor):
        return torch.sign(input_tensor)

    @staticmethod
    def backward(ctx, grad_output):
        return grad_output.clamp_(-1, 1)


apply_sign_ste = LBSign.apply


class DENSEQuantizer(nn.Module):
    """
    A simple quantization module using linear layers and sign function (STE).
    """

    def __init__(self, feature_dim: int, code_dim: int):
        super(DENSEQuantizer, self).__init__()
        self.feature_dim = feature_dim
        self.code_dim = code_dim

        self.feature_to_code_proj = nn.Linear(feature_dim, code_dim)
        self.code_to_feature_proj = nn.Linear(code_dim, feature_dim)

    def quantize_features_to_bits(self, features: torch.Tensor) -> torch.Tensor:
        # Input features: (..., feature_dim)
        # Output bits: (..., code_dim)
        if features.shape[-1] != self.feature_dim:
            raise ValueError(
                f"Input feature_dim {features.shape[-1]} does not match DENSEQuantizer.feature_dim {self.feature_dim}"
            )
        pre_quant_code = self.feature_to_code_proj(features)
        bits = apply_sign_ste(pre_quant_code)
        return bits

    def dequantize_bits_to_features(
        self, bits_after_channel: torch.Tensor
    ) -> torch.Tensor:
        # Input bits_after_channel: (..., code_dim)
        # Output reconstructed_features: (..., feature_dim)
        if bits_after_channel.shape[-1] != self.code_dim:
            raise ValueError(
                f"Input bits_after_channel dim {bits_after_channel.shape[-1]} does not match DENSEQuantizer.code_dim {self.code_dim}"
            )
        cleaned_bits = apply_sign_ste(bits_after_channel)
        reconstructed_features = self.code_to_feature_proj(cleaned_bits)
        return reconstructed_features


if __name__ == "__main__":
    feature_dim = 512
    code_dim = 32
    batch_size = 4
    seq_len = 64

    quantizer = DENSEQuantizer(feature_dim, code_dim)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    quantizer = quantizer.to(device)

    dummy_features1 = torch.randn(batch_size, seq_len, feature_dim).to(device)

    bits1 = quantizer.quantize_features_to_bits(dummy_features1)
    print(f"Shape of bits1: {bits1.shape}")
    print(f"Example bits1 values (should be +/-1): \n{bits1[0, 0, :5]}")

    combined_signal_ideal = (
        bits1 + bits1
    )  # Example: user1's bits summed with themselves
    noisy_combined_signal = (
        combined_signal_ideal + torch.randn_like(combined_signal_ideal) * 0.5
    )
    print(f"\nShape of noisy_combined_signal: {noisy_combined_signal.shape}")

    quantized_after_channel_processing = quantizer.quantize_combined_bits(
        noisy_combined_signal
    )
    print(
        f"Shape of quantized_after_channel_processing: {quantized_after_channel_processing.shape}"
    )
    print(
        f"Example quantized_after_channel_processing values (should be +/-1): \n{quantized_after_channel_processing[0, 0, :5]}"
    )

    reconstructed_features = quantizer.dequantize_bits_to_features(
        quantized_after_channel_processing
    )
    print(f"\nShape of reconstructed_features: {reconstructed_features.shape}")

    print("\nTest LBSign STE:")
    test_tensor = torch.tensor(
        [-2.0, -0.5, 0.0, 0.5, 2.0], requires_grad=True, device=device
    )
    signed_tensor = apply_sign_ste(test_tensor)
    print(f"Original: {test_tensor.data}")
    print(f"Signed:   {signed_tensor.data}")
    signed_tensor.sum().backward()
    print(f"Gradient of original tensor: {test_tensor.grad}")
