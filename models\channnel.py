# semantic_communication/models/channel.py
import math  # For math.sqrt if needed, though torch.sqrt is preferred for tensors

import torch
import torch.nn as nn


class Channel(nn.Module):
    """
    模拟无线信道。
    这个版本基于原始的 `net/channel.py`，它接收一个输入特征，
    并模拟两个输出路径 (例如，一个信号经过两个不同的衰落系数到达两个点，或者被两个接收器接收)。
    """

    def __init__(self, args_config):  # 接收配置对象
        super(Channel, self).__init__()
        self.channel_type_str = args_config.channel_type.lower()  # 'awgn' or 'rayleigh'
        self.device = args_config.device

        # h_dist 和 self.h 在原始代码中存在，但似乎未在 forward 中直接使用。
        # Rayleigh 衰落系数通常在每次信道通过时重新生成。
        # self.h_dist = torch.distributions.normal.Normal(0, 1) # For generating Rayleigh components
        # self.h_fixed_example = torch.sqrt(torch.randn(1)**2 + torch.randn(1)**2) / 1.414 # Example fixed h

        if hasattr(args_config, "logger") and args_config.logger:
            args_config.logger.info(
                f"【信道模型】: 已构建 {self.channel_type_str} 信道, "
                f"SNR(s): {args_config.multiple_snr_str} dB."
            )

    def _complex_normalize_single_input(
        self, x_real_features: torch.Tensor, target_power: float = 1.0
    ):
        """
        对输入的实数特征进行功率归一化，假设它代表复数信号的I和Q部分。
        Args:
            x_real_features (torch.Tensor): 形状为 (B, L*D) 或 (B, N_elements) 的实数特征。
                                           假设前一半是实部，后一半是虚部。
            target_power (float): 归一化后的目标平均功率。
        Returns:
            tuple: (normalized_complex_signal, original_power_per_symbol_pair)
                   normalized_complex_signal 是复数张量。
                   original_power_per_symbol_pair 是原始信号中每个I/Q对的平均功率。
        """
        if x_real_features.ndim != 2:
            raise ValueError(
                f"输入特征应为2D (Batch, Features), 得到 {x_real_features.ndim}D"
            )

        num_elements = x_real_features.shape[1]
        if num_elements % 2 != 0:
            raise ValueError(f"特征数量 ({num_elements}) 必须是偶数，以形成I/Q对。")

        # 计算原始信号的平均功率
        # E[x_I^2 + x_Q^2] = E[x_I^2] + E[x_Q^2]
        # 如果 E[x_I^2] = E[x_Q^2] = P_single, 则 E[|x|^2] = 2 * P_single
        # torch.mean(x_real_features ** 2) 是 E[x_k^2] (k是任一元素)
        # 所以，原始平均功率 per I/Q pair (symbol) 是 2 * torch.mean(x_real_features ** 2)
        original_power_per_symbol_pair = 2.0 * torch.mean(x_real_features**2)

        if original_power_per_symbol_pair.item() < 1e-9:  # 避免除以非常小的值
            # 如果功率接近于零，则不进行缩放或返回零 (取决于期望行为)
            # print("警告: 输入信道的信号功率非常低。")
            # 为了简单，这里我们仍然尝试归一化，但要注意潜在的NaN
            normalized_x = (
                x_real_features  # 不缩放，或乘以 sqrt(target_power) / epsilon
            )
        else:
            scale_factor = torch.sqrt(target_power / original_power_per_symbol_pair)
            normalized_x = x_real_features * scale_factor

        # 转换为复数: B x (N_elements/2)
        complex_signal = (
            normalized_x[:, : num_elements // 2]
            + 1j * normalized_x[:, num_elements // 2 :]
        )
        return complex_signal, original_power_per_symbol_pair

    def forward(
        self,
        input_features: torch.Tensor,
        h1_channel_coeff: torch.Tensor,  # 第一个路径的信道系数 (复数)
        h2_channel_coeff: torch.Tensor,  # 第二个路径的信道系数 (复数)
        p1_power_alloc: float,  # 第一个路径的功率分配因子 (未使用在此版本的信道中)
        p2_power_alloc: float,  # 第二个路径的功率分配因子 (未使用在此版本的信道中)
        snr_db_val: float,
        avg_pwr_normalize_input: bool = False,  # 原始avg_pwr参数，这里改为更明确的名称
        input_already_complex: bool = False,
    ):
        """
        将输入特征通过模拟信道。

        Args:
            input_features (torch.Tensor): 输入特征。
                如果 input_already_complex=False (默认): 形状 (B, N_elements)，实数，会被归一化并转为复数。
                如果 input_already_complex=True: 形状 (B, N_complex_symbols)，复数，会被直接使用（仍会进行功率归一化）。
            h1_channel_coeff (torch.Tensor): 第一个路径的复数信道衰落系数 (标量或可广播的)。
            h2_channel_coeff (torch.Tensor): 第二个路径的复数信道衰落系数 (标量或可广播的)。
            p1_power_alloc (float): 功率分配给路径1 (此版本未使用，但保留接口兼容性)。
            p2_power_alloc (float): 功率分配给路径2 (此版本未使用，但保留接口兼容性)。
            snr_db_val (float): 当前的信噪比 (dB)。
            avg_pwr_normalize_input (bool): 如果为True，则输入信号功率会被归一化到1。
                                           原始代码中，如果avg_pwr=False(默认)，则归一化到1；
                                           如果avg_pwr=True (传入具体功率值)，则归一化到该值。
                                           这里简化为：如果此参数为True，则归一化到单位功率。
            input_already_complex (bool): 如果True，则假定 input_features 已经是复数张量。
        Returns:
            tuple: (noisy_output1_real, noisy_output2_real)
                   两个路径的噪声输出，都是实数张量，形状与输入 input_features 相同。
        """

        original_input_shape = input_features.shape

        if input_already_complex:
            if not torch.is_complex(input_features):
                raise ValueError(
                    "input_already_complex is True, but input_features is not a complex tensor."
                )
            # 如果已经是复数，仍需归一化
            # E[|x_complex|^2]
            power_complex_in = torch.mean(torch.abs(input_features) ** 2)
            if avg_pwr_normalize_input:  # 归一化到单位功率
                if power_complex_in.item() > 1e-9:
                    scale_factor = torch.sqrt(1.0 / power_complex_in)
                    channel_tx_complex = input_features * scale_factor
                else:
                    channel_tx_complex = input_features  # 功率过低，不缩放
            else:  # 不强制归一化到1，保留原始功率（但后续噪声是基于单位信号功率计算的）
                channel_tx_complex = input_features

            original_power_val = power_complex_in  # 保存原始功率
        else:  # 输入是实数，需要转换和归一化
            if input_features.ndim != 2:  # _complex_normalize_single_input 期望2D
                input_features_flat = input_features.view(original_input_shape[0], -1)
            else:
                input_features_flat = input_features

            target_norm_power = (
                1.0 if avg_pwr_normalize_input else 1.0
            )  # 总是归一化到1，除非avg_pwr_normalize_input=False且不希望归一化
            channel_tx_complex, original_power_val = (
                self._complex_normalize_single_input(
                    input_features_flat, target_power=target_norm_power
                )
            )

        # --- 信道效应 (AWGN 或 Rayleigh) ---
        # 噪声功率计算: sigma^2 = N0/2。 SNR = SignalPower / NoisePowerTotal = Ps / (2 * sigma^2 * num_complex_dims_for_noise_calc)
        # 假设信号功率 Ps = 1 (因为已归一化)
        # SNR_linear = 10^(SNR_dB / 10)
        # 1 / (2 * sigma^2) = SNR_linear  => sigma^2 = 1 / (2 * SNR_linear)
        # sigma = sqrt(1 / (2 * 10^(SNR_dB / 10)))

        snr_linear = 10 ** (snr_db_val / 10.0)
        sigma_noise_per_component = math.sqrt(1.0 / (2.0 * snr_linear))

        # 生成噪声 (与信号形状相同)
        noise_shape = channel_tx_complex.shape
        noise_real_comp = (
            torch.randn(noise_shape, device=self.device) * sigma_noise_per_component
        )
        noise_imag_comp = (
            torch.randn(noise_shape, device=self.device) * sigma_noise_per_component
        )
        complex_noise = noise_real_comp + 1j * noise_imag_comp

        # 应用信道系数和噪声
        if self.channel_type_str == "awgn":
            # AWGN: h1, h2 通常是 1 (或由外部提供)
            # 确保 h1, h2 是复数且在正确设备上
            h1 = torch.as_tensor(
                h1_channel_coeff, dtype=torch.complex64, device=self.device
            )
            h2 = torch.as_tensor(
                h2_channel_coeff, dtype=torch.complex64, device=self.device
            )

            # y = h*x + n
            received_signal1_complex = h1 * channel_tx_complex + complex_noise
            received_signal2_complex = (
                h2 * channel_tx_complex + complex_noise
            )  # 假设两个路径有独立噪声实例，但sigma相同
        # 如果噪声应该相同，则只用 complex_noise
        elif self.channel_type_str == "rayleigh":
            # Rayleigh: h1, h2 是复数衰落系数
            h1 = torch.as_tensor(
                h1_channel_coeff, dtype=torch.complex64, device=self.device
            )
            h2 = torch.as_tensor(
                h2_channel_coeff, dtype=torch.complex64, device=self.device
            )

            received_signal1_complex = h1 * channel_tx_complex + complex_noise
            received_signal2_complex = (
                h2 * channel_tx_complex + complex_noise
            )  # 同上关于噪声的假设
        else:
            raise ValueError(f"未知的信道类型: {self.channel_type_str}")

        # --- 信道均衡和反归一化 (可选) ---
        # 原始代码中，瑞利信道后会除以 h (y/h = x + n/h)，模拟理想接收机均衡
        # AWGN信道则没有这一步，因为h=1
        # 功率反归一化：乘以 sqrt(pwr1) / sqrt(p1)
        # p1, p2 在此版本中未使用，所以只考虑 sqrt(original_power_val)

        if self.channel_type_str == "rayleigh":
            # 均衡：除以信道系数 (理想情况)
            # 注意：如果h1或h2非常小，这会导致噪声放大。
            # 添加一个小的epsilon防止除以零。
            epsilon = 1e-9
            equalized_signal1 = received_signal1_complex / (h1 + epsilon)
            equalized_signal2 = received_signal2_complex / (h2 + epsilon)
        else:  # AWGN
            equalized_signal1 = received_signal1_complex
            equalized_signal2 = received_signal2_complex

        # 转换为实数输出，并调整回原始特征的形状
        output1_real = torch.cat(
            [torch.real(equalized_signal1), torch.imag(equalized_signal1)], dim=-1
        )
        output2_real = torch.cat(
            [torch.real(equalized_signal2), torch.imag(equalized_signal2)], dim=-1
        )

        if output1_real.shape != original_input_shape:
            output1_real = output1_real.view(original_input_shape)
        if output2_real.shape != original_input_shape:
            output2_real = output2_real.view(original_input_shape)

        # 反功率归一化 (如果输入时归一化了)
        # 原始代码是 channel_Rx1 * torch.sqrt(pwr1) (假设 p1=1)
        # 这里 pwr1 是 original_power_val (每个I/Q对的功率)
        # 如果我们输出了实数，那么反归一化的因子应该是针对实数元素的标准差
        # 原始实数特征的标准差是 sqrt(original_power_val / 2)
        # 归一化后的实数特征标准差是 sqrt(target_norm_power / 2)
        # 所以反归一化因子是 sqrt(original_power_val / target_norm_power)
        if avg_pwr_normalize_input and original_power_val.item() > 1e-9:
            # target_norm_power 之前是 1.0
            denorm_scale = torch.sqrt(original_power_val / 1.0)
            output1_real = output1_real * denorm_scale
            output2_real = output2_real * denorm_scale

        return output1_real, output2_real


if __name__ == "__main__":
    # --- 单元测试和示例 ---
    class MockConfig:
        def __init__(self, channel_type="awgn", snr_str="10", device_val="cpu"):
            self.channel_type = channel_type
            self.multiple_snr_str = snr_str
            self.device = torch.device(device_val)
            self.logger = None  # 可以设置一个 mock logger

    cfg_awgn = MockConfig(channel_type="awgn", snr_str="20")
    channel_awgn = Channel(cfg_awgn)

    cfg_rayleigh = MockConfig(channel_type="rayleigh", snr_str="15")
    channel_rayleigh = Channel(cfg_rayleigh)

    # 创建测试特征 (Batch=2, 64个实数元素 -> 32个复数符号)
    test_feat_real = (
        torch.randn(2, 64, device=cfg_awgn.device) * 2.0
    )  # 增加一些初始功率
    snr_db = 10.0

    # AWGN 测试
    print("--- AWGN 信道测试 ---")
    # 对于AWGN, h1, h2 通常是 1.0
    h1_awgn = torch.tensor(1.0 + 0j, device=cfg_awgn.device)
    h2_awgn = torch.tensor(1.0 + 0j, device=cfg_awgn.device)
    noisy_out1_awgn, noisy_out2_awgn = channel_awgn(
        test_feat_real, h1_awgn, h2_awgn, 1.0, 1.0, snr_db, avg_pwr_normalize_input=True
    )
    print(
        f"AWGN 输出1形状: {noisy_out1_awgn.shape}, 输出2形状: {noisy_out2_awgn.shape}"
    )

    # 计算接收信号的SNR (近似)
    # 假设输入信号归一化到功率1 (复数信号)
    # 噪声功率 sigma_n^2 = 1 / (10^(snr_db/10)) (这是总噪声功率 per complex symbol)
    # 接收信号功率 (如果h=1) 约等于 1。
    # SNR_out = ReceivedSignalPower / NoisePower
    # 这里的 noisy_out 是均衡和反归一化后的，所以直接比较可能不直接反映信道SNR

    # Rayleigh 测试
    print("\n--- Rayleigh 信道测试 ---")
    h1_ray = (
        torch.sqrt(
            torch.randn(1, device=cfg_rayleigh.device) ** 2
            + torch.randn(1, device=cfg_rayleigh.device) ** 2
        )
        / 1.414
        + 0j
    )
    h2_ray = (
        torch.sqrt(
            torch.randn(1, device=cfg_rayleigh.device) ** 2
            + torch.randn(1, device=cfg_rayleigh.device) ** 2
        )
        / 1.414
        + 0j
    )
    h1_ray = (
        h1_ray
        if torch.abs(h1_ray) > 1e-6
        else torch.tensor(1.0 + 0j, device=cfg_rayleigh.device)
    )  # 避免h为0
    h2_ray = (
        h2_ray
        if torch.abs(h2_ray) > 1e-6
        else torch.tensor(1.0 + 0j, device=cfg_rayleigh.device)
    )

    noisy_out1_ray, noisy_out2_ray = channel_rayleigh(
        test_feat_real, h1_ray, h2_ray, 1.0, 1.0, snr_db, avg_pwr_normalize_input=True
    )
    print(
        f"Rayleigh 输出1形状: {noisy_out1_ray.shape}, 输出2形状: {noisy_out2_ray.shape}"
    )

    # 测试输入已是复数的情况
    print("\n--- 测试 input_already_complex ---")
    test_feat_complex = (
        torch.randn(2, 32, dtype=torch.complex64, device=cfg_awgn.device) * 2.0
    )
    noisy_out1_cplx, noisy_out2_cplx = channel_awgn(
        test_feat_complex,
        h1_awgn,
        h2_awgn,
        1.0,
        1.0,
        snr_db,
        avg_pwr_normalize_input=True,
        input_already_complex=True,
    )
    # 输出仍是实数 (I,Q 分开)
    print(f"AWGN (复数输入) 输出1形状: {noisy_out1_cplx.shape}")
    # 预期输出形状应与原始 test_feat_real 相同 (2,64)
    assert noisy_out1_cplx.shape == test_feat_real.shape
