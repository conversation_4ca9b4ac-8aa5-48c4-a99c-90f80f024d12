#!/usr/bin/env python3
"""
调试交织器的维度变化
"""

import torch
import sys
sys.path.append('.')

from models.interleaver import BlockInterleaver

def debug_interleaver():
    """调试交织器的维度变化"""
    
    interleaver = BlockInterleaver()
    
    # 测试不同的输入形状
    test_shapes = [
        (1, 128),  # batch_size=1, code_dim=128
        (2, 128),  # batch_size=2, code_dim=128
        (4, 128),  # batch_size=4, code_dim=128
    ]
    
    for batch_size, code_dim in test_shapes:
        print(f"\n测试形状: ({batch_size}, {code_dim})")
        
        # 创建测试输入
        x = torch.randn(batch_size, code_dim)
        print(f"原始输入形状: {x.shape}")
        
        # 交织
        x_interleaved, metadata = interleaver.interleave(x)
        print(f"交织后形状: {x_interleaved.shape}")
        print(f"metadata: {metadata}")
        
        # 解交织
        x_deinterleaved = interleaver.deinterleave(x_interleaved, metadata)
        print(f"解交织后形状: {x_deinterleaved.shape}")
        
        # 验证是否相等
        print(f"是否相等: {torch.allclose(x, x_deinterleaved)}")

if __name__ == "__main__":
    debug_interleaver()
