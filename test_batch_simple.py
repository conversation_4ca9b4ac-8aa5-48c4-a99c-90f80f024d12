#!/usr/bin/env python3
"""
简化的批量推理测试脚本
"""

import numpy as np
import onnxruntime as ort
import time

def test_batch_inference():
    """测试批量推理"""
    
    # 加载ONNX模型
    print("加载ONNX模型...")
    transmitter_session = ort.InferenceSession("transmitter_model.onnx")
    receiver_session = ort.InferenceSession("receiver_model.onnx")
    
    print("发送端输入:", [input.name + ": " + str(input.shape) for input in transmitter_session.get_inputs()])
    print("发送端输出:", [output.name + ": " + str(output.shape) for output in transmitter_session.get_outputs()])
    print("接收端输入:", [input.name + ": " + str(input.shape) for input in receiver_session.get_inputs()])
    print("接收端输出:", [output.name + ": " + str(output.shape) for output in receiver_session.get_outputs()])
    
    # 测试不同的批次大小
    batch_sizes = [1, 2, 4, 8]
    
    for batch_size in batch_sizes:
        print(f"\n测试批次大小: {batch_size}")
        
        # 创建批量输入
        input1 = np.random.randn(batch_size, 3, 64, 64).astype(np.float32)
        input2 = np.random.randn(batch_size, 3, 64, 64).astype(np.float32)
        
        try:
            # 发送端推理
            start_time = time.time()
            transmitter_outputs = transmitter_session.run(None, {"input1": input1, "input2": input2})
            transmitter_time = time.time() - start_time
            
            interleaved_feature = transmitter_outputs[0]
            print(f"  发送端: {transmitter_time:.4f}秒, 输出形状: {interleaved_feature.shape}")
            
            # 接收端推理
            start_time = time.time()
            receiver_outputs = receiver_session.run(None, {"interleaved_feature": interleaved_feature})
            receiver_time = time.time() - start_time
            
            output1, output2 = receiver_outputs
            print(f"  接收端: {receiver_time:.4f}秒, 输出形状: {output1.shape}, {output2.shape}")
            
            total_time = transmitter_time + receiver_time
            throughput = batch_size / total_time
            print(f"  总时间: {total_time:.4f}秒, 吞吐量: {throughput:.2f} 图像/秒")
            
        except Exception as e:
            print(f"  批次大小 {batch_size} 失败: {e}")

if __name__ == "__main__":
    test_batch_inference()
