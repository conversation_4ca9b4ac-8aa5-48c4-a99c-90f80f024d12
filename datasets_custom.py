# datasets_custom.py
import os
import csv
import torch.utils.data as data
from PIL import Image

def pil_loader(path):
    # open path as file to avoid ResourceWarning (https://github.com/python-pillow/Pillow/issues/835)
    # Borrowed from https://github.com/pytorch/vision/blob/master/torchvision/datasets/folder.py
    with open(path, 'rb') as f:
        img = Image.open(f)
        return img.convert('RGB')

class MiniImagenet(data.Dataset):
    """
    Custom MiniImagenet dataset.
    Assumes the dataset is already downloaded and placed in a 'tmp/miniimagenet'
    directory relative to where the script is run, or that the 'root' argument
    points to the correct pre-processed MiniImagenet directory.

    The original download logic relied on a specific local structure
    (self.base_folder = 'tmp/miniimagenet'). For broader usability,
    it's better if the user provides the direct path to the extracted dataset.
    The download functionality is simplified here and might need adjustment
    if the files aren't in the expected locations.
    """
    splits = {
        'train': 'train.csv',
        'valid': 'val.csv', # 'val.csv' is often used, 'valid' was in original
        'test': 'test.csv'
    }

    def __init__(self, root, split='train',
                 transform=None, target_transform=None, download=False):
        super(MiniImagenet, self).__init__()
        self.root = os.path.expanduser(root)
        self.transform = transform
        self.target_transform = target_transform
        self.split = split

        if self.split not in self.splits:
            raise ValueError(f'Split must be one of {list(self.splits.keys())}')

        self.image_folder = os.path.join(self.root, 'images')
        self.split_filename = os.path.join(self.root, self.splits[self.split])

        if download:
            self.download_miniimagenet() # Basic placeholder for download logic

        if not self._check_exists():
            raise RuntimeError('Dataset not found at {}. You might need to '
                               'download it manually or ensure paths are correct.'.format(self.root))

        # Extract filenames and labels
        self._data = []
        with open(self.split_filename, 'r') as f:
            reader = csv.reader(f)
            try:
                header = next(reader) # Skip the header
                if header != ['filename', 'label']: # Basic check for expected CSV format
                    print(f"Warning: CSV header is {header}, expected ['filename', 'label']")
            except StopIteration:
                raise ValueError(f"CSV file {self.split_filename} is empty or has no header.")

            for line in reader:
                if len(line) == 2: # Ensure it's filename, label
                    self._data.append(tuple(line))
                else:
                    print(f"Warning: Skipping malformed line in CSV: {line}")

        if not self._data:
            raise ValueError(f"No data loaded from {self.split_filename}. Check CSV content and format.")

        self._fit_label_encoding()

    def __getitem__(self, index):
        filename, label_str = self._data[index]
        try:
            image = pil_loader(os.path.join(self.image_folder, filename))
        except FileNotFoundError:
            raise FileNotFoundError(f"Image file not found: {os.path.join(self.image_folder, filename)}. "
                                    f"Ensure 'images' subfolder exists in '{self.root}' and contains the images.")

        label_idx = self._label_encoder[label_str]

        if self.transform is not None:
            image = self.transform(image)
        if self.target_transform is not None:
            label_idx = self.target_transform(label_idx) # Assuming target_transform works with numerical labels

        return image, label_idx

    def _fit_label_encoding(self):
        if not self._data: # Should not happen if constructor checks worked
            self._label_encoder = {}
            self.num_classes = 0
            return

        _, labels_str_list = zip(*self._data)
        # Sort unique labels to ensure consistent encoding across runs/machines
        unique_labels_str = sorted(list(set(labels_str_list)))
        self._label_encoder = {label_str: idx for idx, label_str in enumerate(unique_labels_str)}
        self.num_classes = len(unique_labels_str)

    def _check_exists(self):
        return (os.path.exists(self.root)
                and os.path.exists(self.image_folder)
                and os.path.exists(self.split_filename))

    def download_miniimagenet(self):
        # This is a placeholder. Real download logic for MiniImagenet is complex
        # and usually involves getting it from a specific source.
        # For this refactor, we assume the user handles dataset acquisition.
        print("Download functionality for MiniImagenet is not fully implemented here.")
        print(f"Please ensure the dataset is available at {self.root} with 'images' subfolder and CSV files.")
        if self._check_exists():
            print("Dataset already found.")
            return True
        
        # Example of how one might structure it if a zip file was expected:
        # from shutil import copyfile
        # from zipfile import ZipFile
        # base_folder_tmp = 'tmp/miniimagenet_download_source' # Imaginary source
        # filename_zip = 'miniimagenet.zip'
        # path_source_zip = os.path.join(base_folder_tmp, filename_zip)
        # path_dest_zip = os.path.join(self.root, filename_zip)

        # if not os.path.exists(self.root):
        #     os.makedirs(self.root)

        # if os.path.exists(path_source_zip): # Check if source zip is available
        #     print(f'Copying {filename_zip} to {self.root}...')
        #     copyfile(path_source_zip, path_dest_zip)
        #     print(f'Extracting {filename_zip} in {self.root}...')
        #     with ZipFile(path_dest_zip, 'r') as f:
        #         f.extractall(self.root)
        #     # Potentially copy CSVs if they are not in the root after extraction
        #     for split_type in self.splits:
        #         src_csv = os.path.join(base_folder_tmp, self.splits[split_type])
        #         dst_csv = os.path.join(self.root, self.splits[split_type])
        #         if os.path.exists(src_csv) and not os.path.exists(dst_csv):
        #             copyfile(src_csv, dst_csv)
        #     print('Download and extraction (simulated) complete.')
        # else:
        #     print(f"Source zip {path_source_zip} not found for download.")
        return self._check_exists()


    def __len__(self):
        return len(self._data)

if __name__ == '__main__':
    # Example usage:
    # You would need to have the MiniImagenet dataset structured correctly in a directory.
    # For example, if your dataset is in '/path/to/miniimagenet_dataset'
    # which contains 'images/' folder and 'train.csv', 'val.csv', 'test.csv'.

    # Create a dummy dataset structure for testing
    dummy_root = './dummy_miniimagenet'
    if not os.path.exists(os.path.join(dummy_root, 'images')):
        os.makedirs(os.path.join(dummy_root, 'images'))

    # Create dummy images
    try:
        Image.new('RGB', (84, 84), color = 'red').save(os.path.join(dummy_root, 'images', 'img1.png'))
        Image.new('RGB', (84, 84), color = 'blue').save(os.path.join(dummy_root, 'images', 'img2.png'))
        Image.new('RGB', (84, 84), color = 'green').save(os.path.join(dummy_root, 'images', 'img3.png'))
    except ImportError:
        print("Pillow (PIL) is required to create dummy images for this test.")


    # Create dummy csv files
    with open(os.path.join(dummy_root, 'train.csv'), 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['filename', 'label'])
        writer.writerow(['img1.png', 'n00000001'])
        writer.writerow(['img2.png', 'n00000002'])

    with open(os.path.join(dummy_root, 'val.csv'), 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['filename', 'label'])
        writer.writerow(['img3.png', 'n00000001'])


    print(f"Attempting to load dataset from: {dummy_root}")
    try:
        # Test train split
        train_dataset = MiniImagenet(root=dummy_root, split='train', download=False) # download=True might run placeholder
        print(f"Number of training samples: {len(train_dataset)}")
        if len(train_dataset) > 0:
            img, label = train_dataset[0]
            print(f"First training sample: Image type {type(img)}, Label {label}")
            print(f"Number of classes: {train_dataset.num_classes}")

        # Test validation split
        val_dataset = MiniImagenet(root=dummy_root, split='valid')
        print(f"Number of validation samples: {len(val_dataset)}")
        if len(val_dataset) > 0:
            img, label = val_dataset[0]
            print(f"First validation sample: Image type {type(img)}, Label {label}")

    except Exception as e:
        print(f"Error during MiniImagenet example: {e}")
        print("Please ensure the dummy dataset structure is correctly created or provide a path to a real MiniImagenet dataset.")

    # Clean up dummy files (optional)
    # import shutil
    # shutil.rmtree(dummy_root)
