# 训练指南 - 阶段化训练系统

本项目支持三阶段训练流程，现在可以一次性运行所有阶段，也可以单独运行每个阶段。

## 新功能特性

### 1. 按阶段命名的文件夹结构
- 不再使用时间戳命名
- 每个阶段有独立的文件夹：`results/{experiment_name}_phase{1,2,3}/`
- 日志文件：`Log_phase{1,2,3}.log`

### 2. 一次运行所有阶段
- 新增 `--run_all_phases` 参数
- 自动从阶段1到阶段3连续训练
- 自动传递前一阶段的最佳模型到下一阶段

## 训练阶段说明

### 阶段1: 核心网络训练
- **目标**: 训练编码器和解码器
- **冻结**: 无（量化器尚未初始化）
- **优化**: 仅核心网络参数
- **输出**: 训练好的编码器和解码器

### 阶段2: 量化器训练
- **目标**: 训练量化器
- **冻结**: 编码器和解码器参数
- **优化**: 仅量化器参数
- **输入**: 阶段1的预训练模型
- **输出**: 训练好的量化器

### 阶段3: 联合微调
- **目标**: 微调所有组件
- **冻结**: 无
- **优化**: 所有参数（编码器、解码器、量化器）
- **输入**: 阶段2的预训练模型
- **输出**: 最终优化的完整模型

## 使用方法

### 方法1: 一次运行所有阶段（推荐）

```bash
# 基本用法
python main.py --run_all_phases --experiment_name my_experiment

# 自定义参数
python main.py --run_all_phases \
    --experiment_name witt_wo_celeba \
    --epochs 10 \
    --lr_core 0.0001 \
    --lr_quantizer 0.00001 \
    --batch_size 32 \
    --trainset celeba \
    --data_path /path/to/celeba \
    --multiple_snr "5,10,15"
```

### 方法2: 单独运行每个阶段

```bash
# 阶段1: 训练核心网络
python main.py --training_phase 1 --experiment_name my_experiment --epochs 10

# 阶段2: 训练量化器（需要阶段1的模型）
python main.py --training_phase 2 --experiment_name my_experiment --epochs 5 \
    --load_pretrained_core ./results/my_experiment_phase1/models/celeba_phase1_epoch10_snr5-10-15.pth

# 阶段3: 联合微调（需要阶段2的模型）
python main.py --training_phase 3 --experiment_name my_experiment --epochs 5 \
    --load_pretrained_core ./results/my_experiment_phase2/models/celeba_phase2_epoch5_snr5-10-15.pth
```

### 方法3: 使用便捷脚本

```bash
# 运行所有阶段
python train_all_phases.py --run_all_phases --experiment_name my_experiment

# 运行单个阶段
python train_all_phases.py --training_phase 1 --experiment_name my_experiment
```

## 文件夹结构

运行后会生成以下文件夹结构：

```
results/
├── my_experiment_phase1/
│   ├── Log_phase1.log
│   ├── models/
│   │   └── celeba_phase1_epoch10_snr5-10-15.pth
│   └── samples/
├── my_experiment_phase2/
│   ├── Log_phase2.log
│   ├── models/
│   │   └── celeba_phase2_epoch5_snr5-10-15.pth
│   └── samples/
└── my_experiment_phase3/
    ├── Log_phase3.log
    ├── models/
    │   └── celeba_phase3_epoch5_snr5-10-15.pth
    └── samples/
```

## 主要参数说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--run_all_phases` | 运行所有三个阶段 | False |
| `--training_phase` | 指定训练阶段 (1,2,3) | 1 |
| `--experiment_name` | 实验名称 | "default_exp" |
| `--epochs` | 每个阶段的训练轮数 | 5 |
| `--lr_core` | 核心网络学习率 | 0.0001 |
| `--lr_quantizer` | 量化器学习率 | 0.00001 |
| `--load_pretrained_core` | 预训练模型路径 | None |

## 注意事项

1. **内存管理**: 连续运行所有阶段可能需要较多内存，建议在训练前清理GPU内存
2. **检查点**: 每个阶段都会保存独立的检查点，可以从任意阶段恢复训练
3. **日志**: 每个阶段有独立的日志文件，便于调试和监控
4. **模型传递**: 使用 `--run_all_phases` 时，会自动选择每个阶段的最佳模型传递给下一阶段

## 故障排除

### 常见问题

1. **找不到预训练模型**: 确保前一阶段已完成训练并生成了模型文件
2. **内存不足**: 减小批次大小或使用更少的工作进程
3. **路径错误**: 检查数据路径和模型路径是否正确

### 恢复训练

如果训练中断，可以使用 `--resume_checkpoint` 参数恢复：

```bash
python main.py --training_phase 2 --experiment_name my_experiment \
    --resume_checkpoint ./results/my_experiment_phase2/models/celeba_phase2_epoch3_snr5-10-15.pth
```
