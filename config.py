# config.py
import os
from datetime import datetime

import torch
import torch.nn as nn


class DatasetConfig:
    """数据集相关的固定配置"""

    CIFAR10 = {
        "image_dims": (3, 32, 32),
        "batch_size": 64,
        "downsample_factor": 3,
        "encoder_kwargs": {
            "img_size": (32, 32),
            "patch_size": 2,
            "in_chans": 3,
            "embed_dims": [256, 512, 1024],
            "depths": [2, 4, 6],
            "num_heads": [2, 4, 8],
            "window_size": 2,
            "mlp_ratio": 4.0,
            "qkv_bias": True,
            "qk_scale": None,
            "norm_layer": nn.LayerNorm,
            "patch_norm": True,
        },
        "decoder_kwargs": {
            "img_size": (32, 32),
            "embed_dims": [1024, 512, 256],
            "depths": [6, 4, 2],
            "num_heads": [8, 4, 2],
            "window_size": 2,
            "mlp_ratio": 4.0,
            "qkv_bias": True,
            "qk_scale": None,
            "norm_layer": nn.LayerNorm,
            "patch_norm": True,
        },
    }

    DIV2K = {
        "image_dims": (3, 256, 256),
        "batch_size": 8,
        "downsample_factor": 4,
        "encoder_kwargs": {
            "img_size": (256, 256),
            "patch_size": 2,
            "in_chans": 3,
            "embed_dims": [128, 192, 256, 320],
            "depths": [2, 2, 6, 2],
            "num_heads": [4, 6, 8, 10],
            "window_size": 8,
            "mlp_ratio": 4.0,
            "qkv_bias": True,
            "qk_scale": None,
            "norm_layer": nn.LayerNorm,
            "patch_norm": True,
        },
        "decoder_kwargs": {
            "img_size": (256, 256),
            "embed_dims": [320, 256, 192, 128],
            "depths": [2, 6, 2, 2],
            "num_heads": [10, 8, 6, 4],
            "window_size": 8,
            "mlp_ratio": 4.0,
            "qkv_bias": True,
            "qk_scale": None,
            "norm_layer": nn.LayerNorm,
            "patch_norm": True,
        },
    }

    CELEBA = {
        "image_dims": (3, 64, 64),
        "batch_size": 32,
        "downsample_factor": 3,
        "encoder_kwargs": {
            "img_size": (64, 64),
            "patch_size": 2,
            "in_chans": 3,
            "embed_dims": [128, 256, 512],
            "depths": [2, 4, 4],
            "num_heads": [2, 4, 8],
            "window_size": 2,
            "mlp_ratio": 4.0,
            "qkv_bias": True,
            "qk_scale": None,
            "norm_layer": nn.LayerNorm,
            "patch_norm": True,
        },
        "decoder_kwargs": {
            "img_size": (64, 64),
            "embed_dims": [512, 256, 128],
            "depths": [4, 4, 2],
            "num_heads": [8, 4, 2],
            "window_size": 2,
            "mlp_ratio": 4.0,
            "qkv_bias": True,
            "qk_scale": None,
            "norm_layer": nn.LayerNorm,
            "patch_norm": True,
        },
    }

    CELEBA256 = {
        "image_dims": (3, 256, 256),
        "batch_size": 4,
        "downsample_factor": 4,
        "encoder_kwargs": {
            "img_size": (256, 256),
            "patch_size": 2,
            "in_chans": 3,
            "embed_dims": [128, 192, 256, 320],
            "depths": [2, 2, 6, 2],
            "num_heads": [4, 6, 8, 10],
            "window_size": 8,
            "mlp_ratio": 4.0,
            "qkv_bias": True,
            "qk_scale": None,
            "norm_layer": nn.LayerNorm,
            "patch_norm": True,
        },
        "decoder_kwargs": {
            "img_size": (256, 256),
            "embed_dims": [320, 256, 192, 128],
            "depths": [2, 6, 2, 2],
            "num_heads": [10, 8, 6, 4],
            "window_size": 8,
            "mlp_ratio": 4.0,
            "qkv_bias": True,
            "qk_scale": None,
            "norm_layer": nn.LayerNorm,
            "patch_norm": True,
        },
    }

    @classmethod
    def get_dataset_config(cls, dataset_name):
        """获取数据集配置"""
        dataset_map = {
            "CIFAR10": cls.CIFAR10,
            "DIV2K": cls.DIV2K,
            "celeba": cls.CELEBA,
            "celeba256": cls.CELEBA256,
            "tinyimagenet": cls.CELEBA,  # 使用与CelebA相同的配置
        }
        if dataset_name not in dataset_map:
            raise ValueError(f"Unsupported dataset: {dataset_name}")
        return dataset_map[dataset_name]


class BaseConfig:
    """基础配置类"""

    def __init__(self, args, training_phase=None):
        # 系统默认参数
        self.seed = 1024
        self.CUDA = torch.cuda.is_available()
        self.device = torch.device("cuda:0" if self.CUDA else "cpu")
        self.print_step = 500
        self.save_model_freq = 1
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 用户可配置参数
        self.experiment_name = args.experiment_name
        self.trainset = args.trainset
        self.testset = args.testset
        self.data_path = args.data_path
        self.model_type = args.model
        self.channel_type = args.channel_type
        self.distortion_metric = args.distortion_metric
        self.bottleneck_dim_C = args.C
        self.multiple_snr_str = args.multiple_snr
        self.num_workers = args.num_workers
        self.quant_code_dim = args.quant_code_dim

        # 训练相关参数（可选）
        self.learning_rate = getattr(args, "lr_core", 0.0001)  # 默认值0.0001
        self.tot_epoch = getattr(args, "epochs", 30)  # 默认值30

        # 确定当前训练阶段
        self.current_phase = (
            training_phase
            if training_phase is not None
            else getattr(args, "training_phase", 1)
        )

        # 获取数据集固定配置
        dataset_config = DatasetConfig.get_dataset_config(self.trainset)
        self.image_dims = dataset_config["image_dims"]
        self.batch_size = (
            args.batch_size
            if hasattr(args, "batch_size") and args.batch_size
            else dataset_config["batch_size"]
        )
        self.test_batch_size = self.batch_size
        self.downsample_factor = dataset_config["downsample_factor"]

        # 设置模型架构参数
        self.encoder_kwargs = dataset_config["encoder_kwargs"].copy()
        self.decoder_kwargs = dataset_config["decoder_kwargs"].copy()
        self.encoder_kwargs["C"] = self.bottleneck_dim_C
        self.decoder_kwargs["C"] = self.bottleneck_dim_C

        # 设置路径 - 按阶段命名
        self.workdir_base = (
            f"./results/{self.experiment_name}_phase{self.current_phase}"
        )
        self.log_file_path = os.path.join(
            self.workdir_base, f"Log_phase{self.current_phase}.log"
        )
        self.samples_dir = os.path.join(self.workdir_base, "samples")
        self.models_dir = os.path.join(self.workdir_base, "models")

        # 更新派生参数
        self.multiple_snr_list = [int(s) for s in self.multiple_snr_str.split(",")]


if __name__ == "__main__":

    class DummyArgs:
        def __init__(self):
            self.experiment_name = "test_exp"
            self.trainset = "celeba"
            self.testset = "kodak"
            self.distortion_metric = "MS-SSIM"
            self.model = "WITT"
            self.channel_type = "awgn"
            self.C = 128
            self.multiple_snr = "0,5,10"
            self.num_workers = 0
            self.batch_size = 16
            self.quant_code_dim = 32
            self.epochs = 50
            self.lr_core = 0.0001

    # 测试配置初始化
    dummy_args = DummyArgs()
    cfg = BaseConfig(dummy_args)

    print("Configuration:")
    for key, value in cfg.__dict__.items():
        if key not in ["encoder_kwargs", "decoder_kwargs"]:
            print(f"  {key}: {value}")

    print("\nEncoder Configuration:")
    for key, value in cfg.encoder_kwargs.items():
        print(f"  {key}: {value}")

    print("\nDecoder Configuration:")
    for key, value in cfg.decoder_kwargs.items():
        print(f"  {key}: {value}")
