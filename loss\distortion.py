# loss/distortion.py
import torch
import torch.nn as nn # Added nn import for MSE class
import torch.nn.functional as F

# Note: Using @torch.jit.script can sometimes make debugging harder.
# It's good for performance once the code is stable.

@torch.jit.script
def create_window(window_size: int, sigma: float, channel: int):
    '''
    Create 1-D Gaussian kernel.
    :param window_size: the size of gauss kernel
    :param sigma: sigma of normal distribution
    :param channel: input channel
    :return: 1D kernel (channel, 1, 1, window_size)
    '''
    coords = torch.arange(window_size, dtype=torch.float32) # Use float32 for consistency
    coords -= window_size // 2

    g = torch.exp(-(coords ** 2) / (2 * sigma ** 2))
    g /= g.sum() # Normalize

    # Reshape to (channel, 1, 1, window_size) for compatibility with conv2d groups
    g = g.reshape(1, 1, 1, -1).repeat(channel, 1, 1, 1)
    return g


@torch.jit.script
def _gaussian_filter(x: torch.Tensor, window_1d: torch.Tensor, use_padding: bool):
    '''
    Blur input with 1-D kernel in X and Y directions.
    :param x: batch of tensors to be blurred (N, C, H, W)
    :param window_1d: 1-D gauss kernel (C, 1, 1, window_size)
    :param use_padding: padding image before conv
    :return: blurred tensors (N, C, H, W)
    '''
    C = x.shape[1]
    padding_h, padding_w = 0, 0
    if use_padding:
        window_size = window_1d.shape[3]
        padding_w = window_size // 2 # Padding for horizontal conv (dim 3)
        padding_h = window_size // 2 # Padding for vertical conv (dim 2)

    # Convolve along width (horizontal)
    # Input: (N, C, H, W), Kernel: (C, 1, 1, W_k) -> Output: (N, C, H, W')
    out = F.conv2d(x, window_1d, stride=1, padding=(0, padding_w), groups=C)
    # Convolve along height (vertical)
    # Input: (N, C, H, W'), Kernel: (C, 1, W_k, 1) which is window_1d.transpose(2,3)
    # Output: (N, C, H', W')
    out = F.conv2d(out, window_1d.transpose(2, 3), stride=1, padding=(padding_h, 0), groups=C)
    return out


@torch.jit.script
def ssim(X: torch.Tensor, Y: torch.Tensor, window: torch.Tensor, data_range: float, use_padding: bool = False):
    '''
    Calculate SSIM index for X and Y.
    :param X: images (N, C, H, W)
    :param Y: images (N, C, H, W)
    :param window: 1-D Gaussian kernel (C, 1, 1, window_size)
    :param data_range: value range of input images. (e.g., 1.0 or 255.0)
    :param use_padding: padding image before conv
    :return: tuple (ssim_val, cs_val)
    '''

    K1 = 0.01
    K2 = 0.03
    # compensation = 1.0 # Used in the original paper to avoid instability for zero variance

    C1 = (K1 * data_range) ** 2
    C2 = (K2 * data_range) ** 2

    mu1 = _gaussian_filter(X, window, use_padding)
    mu2 = _gaussian_filter(Y, window, use_padding)

    mu1_sq = mu1.pow(2) 
    mu2_sq = mu2.pow(2)
    mu1_mu2 = mu1 * mu2


    sigma1_sq_raw = _gaussian_filter(X * X, window, use_padding)
    sigma2_sq_raw = _gaussian_filter(Y * Y, window, use_padding)
    sigma12_raw = _gaussian_filter(X * Y, window, use_padding)

    sigma1_sq = sigma1_sq_raw - mu1_sq
    sigma2_sq = sigma2_sq_raw - mu2_sq
    sigma12 = sigma12_raw - mu1_mu2
    
    # Numerator of SSIM
    ssim_num = (2 * mu1_mu2 + C1) * (2 * sigma12 + C2)
    # Denominator of SSIM
    ssim_den = (mu1_sq + mu2_sq + C1) * (sigma1_sq + sigma2_sq + C2)

    ssim_map = ssim_num / (ssim_den + 1e-8) # Add epsilon to denominator for stability
    ssim_val = ssim_map.mean(dim=(1, 2, 3)) # reduce along C, H, W

    # Contrast-structure (cs) part
    cs_map = (2 * sigma12 + C2) / (sigma1_sq + sigma2_sq + C2 + 1e-8) # Add epsilon
    cs_map = F.relu(cs_map) 
    cs_val = cs_map.mean(dim=(1,2,3))

    return ssim_val, cs_val


@torch.jit.script
def ms_ssim(X: torch.Tensor, Y: torch.Tensor, window: torch.Tensor, data_range: float, weights: torch.Tensor, use_padding: bool = False, eps: float = 1e-8):
    '''
    Interface of MS-SSIM.
    :param X: a batch of images, (N,C,H,W)
    :param Y: a batch of images, (N,C,H,W)
    :param window: 1-D Gaussian kernel
    :param data_range: value range of input images. (e.g., 1.0 or 255)
    :param weights: weights for different levels (scales)
    :param use_padding: padding image before conv
    :param eps: small value to avoid grad NaN for pow operation.
    :return: MS-SSIM value (N)
    '''
    if X.size() != Y.size():
        raise ValueError(f"Input images X and Y must have the same dimensions, but got {X.size()} and {Y.size()}")

    levels = weights.shape[0]
    mcs = [] 

    for i in range(levels):
        ssim_val_i, cs_val_i = ssim(X, Y, window=window, data_range=data_range, use_padding=use_padding)

        if i < levels - 1:
            mcs.append(cs_val_i) 
            X = F.avg_pool2d(X, kernel_size=2, stride=2, ceil_mode=True) 
            Y = F.avg_pool2d(Y, kernel_size=2, stride=2, ceil_mode=True)
        else:
            mcs.append(ssim_val_i) 

    mcs_tensor = torch.stack(mcs, dim=0) 
    mcs_tensor = torch.clamp(mcs_tensor, min=eps)
    ms_ssim_val = torch.prod((mcs_tensor ** weights.unsqueeze(1)), dim=0)

    return ms_ssim_val


class SSIMLoss(torch.jit.ScriptModule):
    __constants__ = ['data_range', 'use_padding', 'size_average']

    def __init__(self, window_size: int = 11, window_sigma: float = 1.5, data_range: float = 1.0, channel: int = 3, use_padding: bool = False, size_average: bool = True):
        super().__init__()
        assert window_size % 2 == 1, 'Window size must be odd.'
        window = create_window(window_size, window_sigma, channel)
        self.register_buffer('window', window)
        self.data_range = data_range
        self.use_padding = use_padding
        self.size_average = size_average 

    @torch.jit.script_method
    def forward(self, X: torch.Tensor, Y: torch.Tensor) -> torch.Tensor:
        ssim_val, _ = ssim(X, Y, window=self.window, data_range=self.data_range, use_padding=self.use_padding)
        loss = 1.0 - ssim_val
        if self.size_average:
            return loss.mean()
        return loss.sum()


class MS_SSIMLoss(torch.jit.ScriptModule):
    __constants__ = ['data_range', 'use_padding', 'eps', 'size_average']

    def __init__(self, window_size: int = 11, window_sigma: float = 1.5, data_range: float = 1.0, channel: int = 3, use_padding: bool = False, weights=None, levels: int = 5, eps: float = 1e-8, size_average: bool = True):
        super().__init__()
        assert window_size % 2 == 1, 'Window size must be odd.'
        self.data_range = data_range
        self.use_padding = use_padding
        self.eps = eps
        self.size_average = size_average

        window = create_window(window_size, window_sigma, channel)
        self.register_buffer('window', window)

        if weights is None: 
            weights = [0.0448, 0.2856, 0.3001, 0.2363, 0.1333]
        
        if levels < 1 or levels > len(weights):
            raise ValueError(f"Levels must be between 1 and {len(weights)}")
        
        current_weights = torch.tensor(weights[:levels], dtype=torch.float32)
        current_weights = current_weights / current_weights.sum() 
        self.register_buffer('weights', current_weights)

    @torch.jit.script_method
    def forward(self, X: torch.Tensor, Y: torch.Tensor) -> torch.Tensor:
        ms_ssim_val = ms_ssim(X, Y, window=self.window, data_range=self.data_range, weights=self.weights, use_padding=self.use_padding, eps=self.eps)
        loss = 1.0 - ms_ssim_val
        if self.size_average:
            return loss.mean()
        return loss.sum()


class MSE(nn.Module): 
    def __init__(self, normalize_input_for_loss: bool = False):
        super(MSE, self).__init__()
        self.mse_loss = nn.MSELoss(reduction='mean') 
        self.normalize_input_for_loss = normalize_input_for_loss

    def forward(self, X: torch.Tensor, Y: torch.Tensor) -> torch.Tensor:
        if X.size() != Y.size():
            raise ValueError(f"Input images X and Y must have the same dimensions, but got {X.size()} and {Y.size()}")

        if self.normalize_input_for_loss: 
            X_proc = (X + 1.0) / 2.0
            Y_proc = (Y + 1.0) / 2.0
        else: 
            X_proc = X
            Y_proc = Y
        
        # Scale to [0, 255] then compute MSE, then normalize back to ~[0,1] scale.
        # This matches common PSNR calculations where MSE is on 0-255 range.
        # The division by 255.0**2 at the end normalizes the loss value.
        # If the loss is directly used for optimization, this scaling might affect LR sensitivity.
        return self.mse_loss(X_proc * 255.0, Y_proc * 255.0) / (255.0**2) 


class Distortion(nn.Module): 
    def __init__(self, args_config): 
        super(Distortion, self).__init__()
        metric = args_config.distortion_metric
        self.data_range = 1.0 # Assuming inputs to loss are in [0,1] for MS-SSIM/SSIM

        if metric == 'MSE':
            self.dist_fn = MSE(normalize_input_for_loss=False)
        elif metric == 'SSIM':
            window_s = 3 if args_config.trainset == 'CIFAR10' else 7 
            self.dist_fn = SSIMLoss(window_size=window_s, data_range=self.data_range, channel=3, use_padding=True)
        elif metric == 'MS-SSIM':
            window_s = 3 if args_config.trainset == 'CIFAR10' else 7 
            self.dist_fn = MS_SSIMLoss(window_size=window_s, data_range=self.data_range, channel=3, use_padding=True, levels=4) 
        else:
            print(f"Warning: Unknown distortion type '{metric}'. Defaulting to MSE.")
            self.dist_fn = MSE(normalize_input_for_loss=False)


    def forward(self, X: torch.Tensor, Y: torch.Tensor) -> torch.Tensor:
        return self.dist_fn(X, Y)


if __name__ == '__main__':
    class DummyConfig:
        distortion_metric = 'MS-SSIM'
        trainset = 'celeba' 

    args_cfg = DummyConfig()

    print("Testing MS_SSIMLoss:")
    ms_ssim_calculator_test = MS_SSIMLoss(data_range=1.0, channel=3, use_padding=True)
    rand_im1_test = torch.rand(4, 3, 64, 64) 
    rand_im2_test = torch.rand(4, 3, 64, 64)
    
    if torch.cuda.is_available():
        ms_ssim_calculator_test = ms_ssim_calculator_test.cuda()
        rand_im1_test = rand_im1_test.cuda()
        rand_im2_test = rand_im2_test.cuda()
    
    loss_val_ms_test = ms_ssim_calculator_test(rand_im1_test, rand_im2_test)
    print(f"MS-SSIM Loss: {loss_val_ms_test.item()}")

    print("\nTesting Distortion class:")
    dist_module_test = Distortion(args_cfg)
    if torch.cuda.is_available():
        dist_module_test = dist_module_test.cuda()

    loss_dist_test = dist_module_test(rand_im1_test, rand_im2_test)
    print(f"Distortion class ({args_cfg.distortion_metric}) Loss: {loss_dist_test.item()}")

    args_cfg.distortion_metric = 'MSE'
    dist_module_mse_test = Distortion(args_cfg)
    if torch.cuda.is_available():
        dist_module_mse_test = dist_module_mse_test.cuda()
    loss_dist_mse_test = dist_module_mse_test(rand_im1_test, rand_im2_test)
    print(f"Distortion class (MSE) Loss: {loss_dist_mse_test.item()}")
    
    mse_direct_test = nn.MSELoss()(rand_im1_test * 255.0, rand_im2_test * 255.0) / (255.0**2)
    print(f"Direct MSE Loss (scaled and normalized): {mse_direct_test.item()}")
