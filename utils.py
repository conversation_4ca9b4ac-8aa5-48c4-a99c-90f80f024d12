# utils.py
import numpy as np
import math
import torch
import random
import os
import logging
import time

class AverageMeter:
    """Compute running average."""
    def __init__(self):
        self.val = 0
        self.avg = 0
        self.sum = 0
        self.count = 0

    def update(self, val, n=1):
        self.val = val
        self.sum += val * n
        self.count += n
        if self.count > 0:
            self.avg = self.sum / self.count
        else:
            self.avg = 0

    def clear(self):
        self.val = 0
        self.avg = 0
        self.sum = 0
        self.count = 0

def logger_configuration(log_file_path, workdir_base, samples_dir, models_dir, save_log=True, test_mode=False): # test_mode currently not used
    logger = logging.getLogger("SemanticCommunicationSystem")
    logger.handlers = [] 
    formatter = logging.Formatter('%(asctime)s - %(levelname)s] %(message)s')
    stdhandler = logging.StreamHandler()
    stdhandler.setLevel(logging.INFO)
    stdhandler.setFormatter(formatter)
    logger.addHandler(stdhandler)
    if save_log:
        makedirs(workdir_base) 
        makedirs(samples_dir)  
        makedirs(models_dir)   
        filehandler = logging.FileHandler(log_file_path)
        filehandler.setLevel(logging.INFO)
        filehandler.setFormatter(formatter)
        logger.addHandler(filehandler)
    logger.setLevel(logging.INFO)
    return logger

def makedirs(directory):
    if not os.path.exists(directory):
        try:
            os.makedirs(directory)
        except Exception as e:
            print(f"Warning: Could not create directory {directory}. Reason: {e}")
            pass

def save_model_checkpoint(state, save_path, filename="checkpoint.pth.tar"):
    makedirs(save_path) # save_path is directory
    filepath = os.path.join(save_path, filename)
    torch.save(state, filepath)
    # print(f"Checkpoint saved to {filepath}") # main.py logger will handle this

def load_individual_model_state(model, model_path_or_state_dict, device):
    """Loads model's state_dict from a path or a state_dict object."""
    state_dict_to_load = None
    if isinstance(model_path_or_state_dict, str): # It's a path
        model_path = model_path_or_state_dict
        if not os.path.isfile(model_path):
            print(f"=> no model state found at '{model_path}'")
            return False # Indicate failure
        # print(f"=> loading model state from '{model_path}'") # main.py logger will handle this
        state_dict_to_load = torch.load(model_path, map_location=device)
    elif isinstance(model_path_or_state_dict, dict): # It's already a state_dict
        state_dict_to_load = model_path_or_state_dict
        # print(f"=> loading model state from provided state_dict object.")
    else:
        print(f"=> Invalid model_path_or_state_dict type: {type(model_path_or_state_dict)}")
        return False # Indicate failure

    if state_dict_to_load is None:
        return False # Indicate failure

    new_state_dict = {}
    for k, v in state_dict_to_load.items():
        if k.startswith('module.'): 
            name = k[7:] 
        else:
            name = k
        new_state_dict[name] = v
            
    try:
        model.load_state_dict(new_state_dict)
        # print(f"=> successfully loaded model state.")
        return True # Indicate success
    except RuntimeError as e:
        print(f"Error loading state_dict for {model.__class__.__name__}: {e}")
        print("Attempting to load with strict=False...")
        try:
            model.load_state_dict(new_state_dict, strict=False)
            print(f"=> successfully loaded model state for {model.__class__.__name__} with strict=False.")
            return True # Indicate success
        except Exception as e2:
            print(f"Failed to load model state for {model.__class__.__name__} even with strict=False: {e2}")
            return False # Indicate failure


def seed_torch(seed=1029):
    random.seed(seed)
    os.environ['PYTHONHASHSEED'] = str(seed) 
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed) 
    # torch.backends.cudnn.benchmark = False # Can be enabled for speed if input sizes are fixed
    # torch.backends.cudnn.deterministic = True # For strict reproducibility

def cal_angle(vector1: torch.Tensor, vector2: torch.Tensor, eps: float = 1e-8) -> torch.Tensor:
    if vector1.ndim == 3 and vector1.shape[1] > 0 : # (B, L, D) -> (B*L, D) if L > 0
        vector1 = vector1.reshape(-1, vector1.shape[-1])
    elif vector1.ndim ==3 and vector1.shape[1] == 0: # (B, 0, D) -> (0, D)
        return torch.tensor(0.0, device=vector1.device) # No angle for empty sequences

    if vector2.ndim == 3 and vector2.shape[1] > 0:
        vector2 = vector2.reshape(-1, vector2.shape[-1])
    elif vector2.ndim ==3 and vector2.shape[1] == 0:
        return torch.tensor(0.0, device=vector2.device)

    if vector1.numel() == 0 or vector2.numel() == 0: # Handle empty tensors after reshape
        return torch.tensor(0.0, device=vector1.device if vector1.numel() > 0 else vector2.device)


    dot_product = torch.sum(vector1 * vector2, dim=1)
    norm1 = torch.norm(vector1, p=2, dim=1)
    norm2 = torch.norm(vector2, p=2, dim=1)
    
    denominator = norm1 * norm2
    valid_mask = denominator > eps

    cos_sim = torch.zeros_like(dot_product) 
    if valid_mask.any(): 
        cos_sim[valid_mask] = dot_product[valid_mask] / denominator[valid_mask]

    cos_sim = torch.clamp(cos_sim, -1.0 + eps, 1.0 - eps)
    angle_rad = torch.acos(cos_sim)
    angle_deg = angle_rad * (180.0 / math.pi)
    
    if valid_mask.any():
        return torch.mean(angle_deg[valid_mask]) 
    else:
        return torch.tensor(0.0, device=vector1.device if vector1.numel() > 0 else vector2.device) 


if __name__ == '__main__':
    avg_meter = AverageMeter()
    avg_meter.update(10, 1)
    avg_meter.update(20, 2) 
    print(f"AverageMeter: val={avg_meter.val}, avg={avg_meter.avg}, sum={avg_meter.sum}, count={avg_meter.count}")

    vec1 = torch.tensor([[1.0, 0.0], [1.0, 1.0]])
    vec2 = torch.tensor([[0.0, 1.0], [1.0, 1.0]]) 
    angle = cal_angle(vec1, vec2)
    print(f"Angle between vec1 and vec2: {angle.item()} degrees (expected approx 45)")

    vec3 = torch.randn(5, 10) 
    vec4 = torch.randn(5, 10)
    angle_batch = cal_angle(vec3, vec4)
    print(f"Mean angle for batch: {angle_batch.item()} degrees")

    vec_empty1 = torch.empty(0,10)
    vec_empty2 = torch.empty(0,10)
    print(f"Angle for empty vectors: {cal_angle(vec_empty1, vec_empty2).item()}")
    
    vec_zero1 = torch.zeros(2,10)
    vec_zero2 = torch.tensor([[0.0]*10, [1.0]*10]) 
    print(f"Angle for zero vectors: {cal_angle(vec_zero1, vec_zero2).item()}")
