#!/usr/bin/env python3
"""
训练脚本示例 - 展示如何使用新的阶段训练功能

使用方法:
1. 运行所有三个阶段:
   python train_all_phases.py --run_all_phases

2. 运行单个阶段:
   python train_all_phases.py --training_phase 1
   python train_all_phases.py --training_phase 2 --load_pretrained_core path/to/phase1/model.pth
   python train_all_phases.py --training_phase 3 --load_pretrained_core path/to/phase2/model.pth

3. 自定义参数运行所有阶段:
   python train_all_phases.py --run_all_phases --experiment_name my_experiment --epochs 10 --lr_core 0.0001
"""

import subprocess
import sys
import argparse
import os

def run_training_command(args_list):
    """运行训练命令"""
    cmd = [sys.executable, "main.py"] + args_list
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("训练成功完成!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"训练失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def main():
    parser = argparse.ArgumentParser(description="训练脚本示例")
    
    # 基本参数
    parser.add_argument("--experiment_name", type=str, default="witt_wo_experiment", 
                       help="实验名称")
    parser.add_argument("--run_all_phases", action="store_true", 
                       help="运行所有三个阶段")
    parser.add_argument("--training_phase", type=int, default=1, choices=[1, 2, 3],
                       help="训练阶段")
    
    # 训练参数
    parser.add_argument("--epochs", type=int, default=5, help="每个阶段的训练轮数")
    parser.add_argument("--lr_core", type=float, default=0.0001, help="核心网络学习率")
    parser.add_argument("--lr_quantizer", type=float, default=0.00001, help="量化器学习率")
    parser.add_argument("--batch_size", type=int, default=32, help="批次大小")
    
    # 数据和模型参数
    parser.add_argument("--trainset", type=str, default="celeba", 
                       choices=["CIFAR10", "DIV2K", "celeba", "tinyimagenet", "celeba256"],
                       help="训练数据集")
    parser.add_argument("--data_path", type=str, default="D:/data/CelebA64", 
                       help="数据路径")
    parser.add_argument("--C", type=int, default=512, help="瓶颈维度")
    parser.add_argument("--quant_code_dim", type=int, default=128, help="量化码维度")
    
    # 通道参数
    parser.add_argument("--channel_type", type=str, default="rayleigh", 
                       choices=["awgn", "rayleigh", "none"], help="信道类型")
    parser.add_argument("--multiple_snr", type=str, default="5,10,15", 
                       help="SNR值列表")
    
    # 损失函数
    parser.add_argument("--distortion_metric", type=str, default="MSE", 
                       choices=["MSE", "MS-SSIM"], help="失真度量")
    
    # 其他参数
    parser.add_argument("--load_pretrained_core", type=str, default=None,
                       help="预训练模型路径")
    parser.add_argument("--num_workers", type=int, default=0, help="数据加载器工作进程数")
    
    args = parser.parse_args()
    
    # 构建基本参数列表
    base_args = [
        "--experiment_name", args.experiment_name,
        "--epochs", str(args.epochs),
        "--lr_core", str(args.lr_core),
        "--lr_quantizer", str(args.lr_quantizer),
        "--trainset", args.trainset,
        "--data_path", args.data_path,
        "--C", str(args.C),
        "--quant_code_dim", str(args.quant_code_dim),
        "--channel_type", args.channel_type,
        "--multiple_snr", args.multiple_snr,
        "--distortion_metric", args.distortion_metric,
        "--num_workers", str(args.num_workers),
    ]
    
    if args.batch_size:
        base_args.extend(["--batch_size", str(args.batch_size)])
    
    if args.run_all_phases:
        # 运行所有阶段
        print("="*60)
        print("开始连续训练所有三个阶段")
        print("="*60)
        
        training_args = base_args + ["--run_all_phases"]
        success = run_training_command(training_args)
        
        if success:
            print("\n" + "="*60)
            print("所有阶段训练完成!")
            print("="*60)
            
            # 显示结果文件夹
            for phase in [1, 2, 3]:
                phase_dir = f"./results/{args.experiment_name}_phase{phase}"
                if os.path.exists(phase_dir):
                    print(f"阶段{phase}结果保存在: {phase_dir}")
        else:
            print("训练失败!")
            return 1
            
    else:
        # 运行单个阶段
        print(f"开始训练阶段 {args.training_phase}")
        
        training_args = base_args + ["--training_phase", str(args.training_phase)]
        
        if args.load_pretrained_core:
            training_args.extend(["--load_pretrained_core", args.load_pretrained_core])
        
        success = run_training_command(training_args)
        
        if success:
            phase_dir = f"./results/{args.experiment_name}_phase{args.training_phase}"
            print(f"\n阶段{args.training_phase}训练完成!")
            print(f"结果保存在: {phase_dir}")
        else:
            print("训练失败!")
            return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
