#!/usr/bin/env python3
"""
ONNX模型推理测试脚本

测试ONNX模型的推理效果，保存原始图片和重构图像进行对比
"""

import argparse
import os
import sys
import time

import numpy as np
import onnxruntime as ort
import torchvision.transforms as transforms
from PIL import Image


def setup_onnx_session(model_path, providers=None):
    """设置ONNX推理会话"""
    if providers is None:
        # 优先使用GPU，如果不可用则使用CPU
        providers = ["CUDAExecutionProvider", "CPUExecutionProvider"]

    try:
        session = ort.InferenceSession(model_path, providers=providers)
        print(f"成功加载模型: {model_path}")
        print(f"使用提供者: {session.get_providers()}")
        return session
    except Exception as e:
        print(f"加载模型失败: {e}")
        return None


def load_and_preprocess_image(image_path, target_size=(64, 64)):
    """加载和预处理图像"""
    try:
        # 加载图像
        image = Image.open(image_path).convert("RGB")

        # 预处理变换
        transform = transforms.Compose(
            [
                transforms.Resize(target_size),
                transforms.ToTensor(),  # 转换为[0,1]范围
            ]
        )

        # 应用变换并添加批次维度
        tensor = transform(image).unsqueeze(0)  # [1, 3, H, W]

        return tensor.numpy().astype(np.float32), image
    except Exception as e:
        print(f"加载图像失败 {image_path}: {e}")
        return None, None


def tensor_to_image(tensor):
    """将张量转换为PIL图像"""
    # 确保张量在[0,1]范围内
    tensor = np.clip(tensor, 0, 1)

    # 移除批次维度并转换为[H, W, C]
    if tensor.ndim == 4:
        tensor = tensor.squeeze(0)  # [3, H, W]
    tensor = np.transpose(tensor, (1, 2, 0))  # [H, W, 3]

    # 转换为[0,255]范围并转换为uint8
    tensor = (tensor * 255).astype(np.uint8)

    return Image.fromarray(tensor)


def calculate_metrics(original, reconstructed):
    """计算重构质量指标"""
    # 确保数据在相同范围内
    original = np.clip(original, 0, 1)
    reconstructed = np.clip(reconstructed, 0, 1)

    # 计算MSE
    mse = np.mean((original - reconstructed) ** 2)

    # 计算PSNR
    if mse == 0:
        psnr = float("inf")
    else:
        psnr = 20 * np.log10(1.0 / np.sqrt(mse))

    return mse, psnr


def test_onnx_inference(
    transmitter_path, receiver_path, image1_path, image2_path, output_dir
):
    """测试ONNX模型推理"""

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 加载ONNX模型
    print("加载ONNX模型...")
    transmitter_session = setup_onnx_session(transmitter_path)
    receiver_session = setup_onnx_session(receiver_path)

    if transmitter_session is None or receiver_session is None:
        print("模型加载失败!")
        return False

    # 加载和预处理图像
    print("加载输入图像...")
    input1_tensor, original_img1 = load_and_preprocess_image(image1_path)
    input2_tensor, original_img2 = load_and_preprocess_image(image2_path)

    if input1_tensor is None or input2_tensor is None:
        print("图像加载失败!")
        return False

    print(f"输入图像1形状: {input1_tensor.shape}")
    print(f"输入图像2形状: {input2_tensor.shape}")

    # 保存原始图像
    original_img1.save(os.path.join(output_dir, "original_image1.png"))
    original_img2.save(os.path.join(output_dir, "original_image2.png"))
    print("已保存原始图像")

    # 发送端推理
    print("执行发送端推理...")
    start_time = time.time()

    try:
        transmitter_outputs = transmitter_session.run(
            None, {"input1": input1_tensor, "input2": input2_tensor}
        )

        # 发送端模型只返回交织特征
        if len(transmitter_outputs) == 1:
            interleaved_feature = transmitter_outputs[0]
            metadata = None  # 接收端会自己处理metadata
        else:
            interleaved_feature, metadata = transmitter_outputs

        transmitter_time = time.time() - start_time
        print(f"发送端推理完成，耗时: {transmitter_time:.4f}秒")
        print(f"交织特征形状: {interleaved_feature.shape}")

    except Exception as e:
        print(f"发送端推理失败: {e}")
        return False

    # 接收端推理
    print("执行接收端推理...")
    start_time = time.time()

    try:
        receiver_outputs = receiver_session.run(
            None, {"interleaved_feature": interleaved_feature}
        )
        output1, output2 = receiver_outputs

        receiver_time = time.time() - start_time
        print(f"接收端推理完成，耗时: {receiver_time:.4f}秒")
        print(f"重构图像1形状: {output1.shape}")
        print(f"重构图像2形状: {output2.shape}")

    except Exception as e:
        print(f"接收端推理失败: {e}")
        return False

    # 转换重构图像为PIL格式并保存
    print("保存重构图像...")
    reconstructed_img1 = tensor_to_image(output1)
    reconstructed_img2 = tensor_to_image(output2)

    reconstructed_img1.save(os.path.join(output_dir, "reconstructed_image1.png"))
    reconstructed_img2.save(os.path.join(output_dir, "reconstructed_image2.png"))

    # 创建对比图像
    print("创建对比图像...")
    create_comparison_images(
        original_img1,
        reconstructed_img1,
        os.path.join(output_dir, "comparison_image1.png"),
    )
    create_comparison_images(
        original_img2,
        reconstructed_img2,
        os.path.join(output_dir, "comparison_image2.png"),
    )

    # 计算质量指标
    print("计算质量指标...")
    mse1, psnr1 = calculate_metrics(input1_tensor, output1)
    mse2, psnr2 = calculate_metrics(input2_tensor, output2)

    # 保存结果报告
    report_path = os.path.join(output_dir, "inference_report.txt")
    with open(report_path, "w", encoding="utf-8") as f:
        f.write("ONNX模型推理测试报告\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"发送端模型: {transmitter_path}\n")
        f.write(f"接收端模型: {receiver_path}\n")
        f.write(f"输入图像1: {image1_path}\n")
        f.write(f"输入图像2: {image2_path}\n\n")
        f.write("推理时间:\n")
        f.write(f"  发送端: {transmitter_time:.4f}秒\n")
        f.write(f"  接收端: {receiver_time:.4f}秒\n")
        f.write(f"  总计: {transmitter_time + receiver_time:.4f}秒\n\n")
        f.write("质量指标:\n")
        f.write(f"  图像1 - MSE: {mse1:.6f}, PSNR: {psnr1:.2f} dB\n")
        f.write(f"  图像2 - MSE: {mse2:.6f}, PSNR: {psnr2:.2f} dB\n")
        f.write(f"  平均PSNR: {(psnr1 + psnr2) / 2:.2f} dB\n\n")
        f.write("输出文件:\n")
        f.write("  - original_image1.png: 原始图像1\n")
        f.write("  - original_image2.png: 原始图像2\n")
        f.write("  - reconstructed_image1.png: 重构图像1\n")
        f.write("  - reconstructed_image2.png: 重构图像2\n")
        f.write("  - comparison_image1.png: 对比图像1\n")
        f.write("  - comparison_image2.png: 对比图像2\n")

    print("\n推理测试完成!")
    print(f"结果保存在: {output_dir}")
    print(f"图像1 - MSE: {mse1:.6f}, PSNR: {psnr1:.2f} dB")
    print(f"图像2 - MSE: {mse2:.6f}, PSNR: {psnr2:.2f} dB")
    print(f"平均PSNR: {(psnr1 + psnr2) / 2:.2f} dB")
    print(f"总推理时间: {transmitter_time + receiver_time:.4f}秒")

    return True


def create_comparison_images(original, reconstructed, output_path):
    """创建原始图像和重构图像的对比图"""
    # 确保两个图像大小相同
    if original.size != reconstructed.size:
        reconstructed = reconstructed.resize(original.size, Image.LANCZOS)

    # 创建并排对比图像
    width, height = original.size
    comparison = Image.new("RGB", (width * 2, height))

    # 粘贴图像
    comparison.paste(original, (0, 0))
    comparison.paste(reconstructed, (width, 0))

    # 保存对比图像
    comparison.save(output_path)


def create_sample_images(output_dir, image_size=(64, 64)):
    """创建示例图像用于测试"""
    print("创建示例图像...")

    # 创建彩色渐变图像1
    img1 = Image.new("RGB", image_size)
    pixels1 = []
    for y in range(image_size[1]):
        for x in range(image_size[0]):
            r = int(255 * x / image_size[0])
            g = int(255 * y / image_size[1])
            b = int(255 * (x + y) / (image_size[0] + image_size[1]))
            pixels1.append((r, g, b))
    img1.putdata(pixels1)

    # 创建几何图案图像2
    img2 = Image.new("RGB", image_size, color="white")
    # 添加一些几何图案
    from PIL import ImageDraw

    draw = ImageDraw.Draw(img2)

    # 绘制圆形
    draw.ellipse([10, 10, 30, 30], fill="red")
    draw.ellipse([35, 35, 55, 55], fill="blue")

    # 绘制矩形
    draw.rectangle([5, 40, 25, 60], fill="green")
    draw.rectangle([40, 5, 60, 25], fill="yellow")

    # 保存示例图像
    sample1_path = os.path.join(output_dir, "sample_image1.png")
    sample2_path = os.path.join(output_dir, "sample_image2.png")

    img1.save(sample1_path)
    img2.save(sample2_path)

    print(f"示例图像已保存: {sample1_path}, {sample2_path}")
    return sample1_path, sample2_path


def main():
    parser = argparse.ArgumentParser(description="测试ONNX模型推理效果")

    # 模型路径
    parser.add_argument(
        "--transmitter_model",
        type=str,
        default="transmitter_model.onnx",
        help="发送端ONNX模型路径",
    )
    parser.add_argument(
        "--receiver_model",
        type=str,
        default="receiver_model.onnx",
        help="接收端ONNX模型路径",
    )

    # 输入图像
    parser.add_argument("--image1", type=str, default=None, help="输入图像1路径")
    parser.add_argument("--image2", type=str, default=None, help="输入图像2路径")

    # 输出设置
    parser.add_argument(
        "--output_dir", type=str, default="./onnx_inference_results", help="输出目录"
    )
    parser.add_argument(
        "--create_samples", action="store_true", help="创建示例图像进行测试"
    )

    # 模型设置
    parser.add_argument(
        "--image_size",
        type=int,
        nargs=2,
        default=[64, 64],
        help="图像尺寸 [高度, 宽度]",
    )

    args = parser.parse_args()

    print("ONNX模型推理测试")
    print("=" * 50)

    # 检查模型文件是否存在
    if not os.path.exists(args.transmitter_model):
        print(f"发送端模型文件不存在: {args.transmitter_model}")
        print("请先运行 convert_to_onnx.py 生成ONNX模型")
        return 1

    if not os.path.exists(args.receiver_model):
        print(f"接收端模型文件不存在: {args.receiver_model}")
        print("请先运行 convert_to_onnx.py 生成ONNX模型")
        return 1

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    # 处理输入图像
    if args.create_samples or (args.image1 is None or args.image2 is None):
        print("使用示例图像进行测试...")
        image1_path, image2_path = create_sample_images(
            args.output_dir, tuple(args.image_size)
        )
    else:
        image1_path = args.image1
        image2_path = args.image2

        # 检查输入图像是否存在
        if not os.path.exists(image1_path):
            print(f"输入图像1不存在: {image1_path}")
            return 1
        if not os.path.exists(image2_path):
            print(f"输入图像2不存在: {image2_path}")
            return 1

    # 执行推理测试
    success = test_onnx_inference(
        args.transmitter_model,
        args.receiver_model,
        image1_path,
        image2_path,
        args.output_dir,
    )

    if success:
        print("\n测试完成! 🎉")
        print(f"请查看输出目录: {args.output_dir}")
        return 0
    else:
        print("\n测试失败! ❌")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
