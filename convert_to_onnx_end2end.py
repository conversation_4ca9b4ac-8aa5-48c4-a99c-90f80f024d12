#!/usr/bin/env python3
"""
端到端ONNX模型转换脚本

创建一个完整的端到端ONNX模型，支持批量推理
"""

import argparse
import os
import sys

import onnx
import torch

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import BaseConfig
from models.core_model import CoreModel
from models.quantization import DENSEQuantizer


def main():
    parser = argparse.ArgumentParser(description="将PyTorch模型转换为端到端ONNX格式")

    # 模型参数
    parser.add_argument("--model_path", type=str, required=True, help="模型检查点路径")
    parser.add_argument("--trainset", type=str, default="celeba", help="训练数据集")
    parser.add_argument("--model", type=str, default="WITT_W/O", help="模型类型")
    parser.add_argument("--C", type=int, default=512, help="瓶颈维度")
    parser.add_argument("--quant_code_dim", type=int, default=128, help="量化码维度")
    parser.add_argument("--channel_type", type=str, default="awgn", help="信道类型")
    parser.add_argument("--multiple_snr", type=str, default="5,10,15", help="多SNR值")
    parser.add_argument("--distortion_metric", type=str, default="MSE", help="失真度量")
    parser.add_argument(
        "--experiment_name", type=str, default="onnx_test", help="实验名称"
    )
    parser.add_argument("--data_path", type=str, default="./data", help="数据路径")

    args = parser.parse_args()

    # 创建配置
    class DummyArgs:
        def __init__(self):
            self.experiment_name = args.experiment_name
            self.trainset = args.trainset
            self.testset = "kodak"
            self.distortion_metric = args.distortion_metric
            self.model = args.model
            self.C = args.C
            self.quant_code_dim = args.quant_code_dim
            self.channel_type = args.channel_type
            self.multiple_snr = args.multiple_snr
            self.data_path = args.data_path
            self.num_workers = 4
            self.batch_size = 8
            self.lr = 1e-4
            self.epochs = 100
            self.save_interval = 10
            self.log_interval = 100
            self.device = "cpu"

    dummy_args = DummyArgs()
    cfg = BaseConfig(dummy_args)
    cfg.device = "cpu"  # 使用CPU进行ONNX转换

    print(f"加载模型检查点: {args.model_path}")

    # 加载检查点
    checkpoint = torch.load(args.model_path, map_location="cpu")
    print("Checkpoint keys:", checkpoint.keys())

    # 从检查点中获取配置
    if "config" in checkpoint:
        saved_cfg = checkpoint["config"]
        print("使用检查点中保存的配置")
        print("保存的配置类型:", type(saved_cfg))

        # 如果是字典，转换为对象
        if isinstance(saved_cfg, dict):

            class ConfigObject:
                def __init__(self, config_dict):
                    for key, value in config_dict.items():
                        setattr(self, key, value)

            cfg = ConfigObject(saved_cfg)
        else:
            cfg = saved_cfg

        cfg.device = "cpu"  # 强制使用CPU
        model_config = cfg  # 直接使用保存的配置
    else:
        print("检查点中没有配置，使用默认配置")

        # 创建默认模型配置
        class DummyModelConfig:
            image_dims = (3, 64, 64)
            downsample_factor = 3
            encoder_kwargs = dict(
                img_size=(64, 64),
                patch_size=2,
                in_chans=3,
                embed_dim=96,
                depths=[2, 2, 6, 2],
                num_heads=[4, 6, 12, 24],
                window_size=7,
                mlp_ratio=4.0,
                qkv_bias=True,
                qk_scale=None,
                drop_rate=0.0,
                attn_drop_rate=0.0,
                drop_path_rate=0.1,
                norm_layer=torch.nn.LayerNorm,
                ape=False,
                patch_norm=True,
                use_checkpoint=False,
            )
            decoder_kwargs = dict(
                img_size=(64, 64),
                patch_size=2,
                in_chans=3,
                embed_dim=96,
                depths=[2, 2, 6, 2],
                num_heads=[4, 6, 12, 24],
                window_size=7,
                mlp_ratio=4.0,
                qkv_bias=True,
                qk_scale=None,
                drop_rate=0.0,
                attn_drop_rate=0.0,
                drop_path_rate=0.1,
                norm_layer=torch.nn.LayerNorm,
                ape=False,
                patch_norm=True,
                use_checkpoint=False,
            )
            C = args.C

        model_config = DummyModelConfig()

    # 创建模型
    net1 = CoreModel(cfg, model_config)
    net2 = CoreModel(cfg, model_config)
    quantizer = DENSEQuantizer(args.C, args.quant_code_dim)

    # 加载状态字典
    net1.load_state_dict(checkpoint["net1_state_dict"])
    net2.load_state_dict(checkpoint["net2_state_dict"])
    quantizer.load_state_dict(checkpoint["quantizer_state_dict"])

    # 设置为评估模式
    net1.eval()
    net2.eval()
    quantizer.eval()

    # 创建虚拟输入
    batch_size = 4  # 使用批次大小4进行测试
    dummy_input1 = torch.randn(batch_size, 3, 64, 64, requires_grad=True).to(cfg.device)
    dummy_input2 = torch.randn(batch_size, 3, 64, 64, requires_grad=True).to(cfg.device)

    # 定义端到端模型（不使用交织器，简化版本）
    class End2EndModel(torch.nn.Module):
        def __init__(self, net1, net2, quantizer):
            super().__init__()
            self.net1 = net1
            self.net2 = net2
            self.quantizer = quantizer

        def forward(self, input1, input2):
            # 编码
            feature1 = self.net1.encode(input1)
            feature2 = self.net2.encode(input2)
            feature = feature1 + feature2

            # 量化
            quantized_feature = self.quantizer.quantize_features_to_bits(feature)

            # 反量化
            dequantized_feature = self.quantizer.dequantize_bits_to_features(
                quantized_feature
            )

            # 解码
            recon_image1 = self.net1.decode(dequantized_feature)
            recon_image2 = self.net2.decode(dequantized_feature)

            return recon_image1, recon_image2

    # 创建端到端模型
    end2end_model = End2EndModel(net1, net2, quantizer)

    print("测试端到端模型...")
    with torch.no_grad():
        test_output1, test_output2 = end2end_model(dummy_input1, dummy_input2)
        print(f"测试输出形状: {test_output1.shape}, {test_output2.shape}")

    # 导出端到端ONNX模型
    print("导出端到端ONNX模型...")
    torch.onnx.export(
        end2end_model,
        (dummy_input1, dummy_input2),
        "end2end_model.onnx",
        export_params=True,
        opset_version=11,
        do_constant_folding=True,
        input_names=["input1", "input2"],
        output_names=["output1", "output2"],
        dynamic_axes={
            "input1": {0: "batch_size"},
            "input2": {0: "batch_size"},
            "output1": {0: "batch_size"},
            "output2": {0: "batch_size"},
        },
        verbose=True,
    )

    # 验证模型
    try:
        onnx.checker.check_model("end2end_model.onnx")
        print("端到端模型验证成功!")
    except onnx.checker.ValidationError as e:
        print("端到端模型验证失败: %s" % e)

    print("端到端ONNX模型已保存为: end2end_model.onnx")


if __name__ == "__main__":
    main()
