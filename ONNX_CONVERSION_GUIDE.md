# ONNX转换指南 - 适用于简化的WITT_W/O模型

本指南说明如何将训练好的WITT_W/O模型转换为ONNX格式，以便在不同平台上部署。

## 更新内容

### 适应WITT_W/O模型的主要变化

1. **移除SNR参数**: 
   - 发送端模型不再需要SNR输入
   - 接收端模型不再需要SNR输入
   - 简化了模型接口

2. **简化输入输出**:
   - 发送端: `(input1, input2)` → `(interleaved_feature, metadata)`
   - 接收端: `(interleaved_feature)` → `(output1, output2)`

3. **修复维度匹配**:
   - 正确计算交织特征的维度
   - 确保量化器维度一致性

## 使用方法

### 基本转换命令

```bash
python convert_to_onnx.py \
    --model_path path/to/your/checkpoint.pth \
    --trainset celeba \
    --model WITT_W/O \
    --C 512 \
    --quant_code_dim 128 \
    --channel_type awgn \
    --multiple_snr "5,10,15" \
    --distortion_metric MSE \
    --experiment_name my_experiment \
    --data_path /path/to/data
```

### 参数说明

| 参数 | 说明 | 示例值 |
|------|------|--------|
| `--model_path` | 训练好的模型检查点路径 | `./results/exp_phase3/models/model.pth` |
| `--trainset` | 训练数据集名称 | `celeba`, `CIFAR10`, `DIV2K` |
| `--model` | 模型类型（现在只支持WITT_W/O） | `WITT_W/O` |
| `--C` | 瓶颈维度 | `512` |
| `--quant_code_dim` | 量化码维度 | `128` |
| `--channel_type` | 信道类型 | `awgn`, `rayleigh`, `none` |
| `--multiple_snr` | SNR值列表 | `"5,10,15"` |
| `--distortion_metric` | 失真度量 | `MSE`, `MS-SSIM` |
| `--experiment_name` | 实验名称 | `my_experiment` |
| `--data_path` | 数据路径 | `/path/to/data` |

### 输出文件

转换成功后会生成两个ONNX文件：

1. **`transmitter_model.onnx`** - 发送端模型
   - 输入: `input1` (图像1), `input2` (图像2)
   - 输出: `interleaved_feature` (交织特征), `metadata` (元数据)

2. **`receiver_model.onnx`** - 接收端模型
   - 输入: `interleaved_feature` (交织特征)
   - 输出: `output1` (重建图像1), `output2` (重建图像2)

## 实际使用示例

### 示例1: 转换阶段3的完整模型

```bash
# 假设你已经完成了三阶段训练
python convert_to_onnx.py \
    --model_path ./results/witt_wo_experiment_phase3/models/celeba_phase3_epoch5_snr5-10-15.pth \
    --trainset celeba \
    --model WITT_W/O \
    --C 512 \
    --quant_code_dim 128 \
    --channel_type rayleigh \
    --multiple_snr "5,10,15" \
    --distortion_metric MSE \
    --experiment_name witt_wo_experiment \
    --data_path D:/data/CelebA64
```

### 示例2: 转换不同配置的模型

```bash
# 小模型配置
python convert_to_onnx.py \
    --model_path ./results/small_model_phase3/models/model.pth \
    --trainset CIFAR10 \
    --model WITT_W/O \
    --C 256 \
    --quant_code_dim 64 \
    --channel_type awgn \
    --multiple_snr "10" \
    --distortion_metric MS-SSIM \
    --experiment_name small_model \
    --data_path ./data/CIFAR10
```

## 部署使用

### Python中使用ONNX模型

```python
import onnxruntime as ort
import numpy as np

# 加载模型
transmitter_session = ort.InferenceSession("transmitter_model.onnx")
receiver_session = ort.InferenceSession("receiver_model.onnx")

# 准备输入数据
input1 = np.random.randn(1, 3, 64, 64).astype(np.float32)
input2 = np.random.randn(1, 3, 64, 64).astype(np.float32)

# 发送端推理
transmitter_outputs = transmitter_session.run(
    None, 
    {"input1": input1, "input2": input2}
)
interleaved_feature, metadata = transmitter_outputs

# 接收端推理
receiver_outputs = receiver_session.run(
    None, 
    {"interleaved_feature": interleaved_feature}
)
output1, output2 = receiver_outputs

print(f"重建图像1形状: {output1.shape}")
print(f"重建图像2形状: {output2.shape}")
```

## 注意事项

### 1. 模型兼容性
- 只支持WITT_W/O模型
- 确保检查点文件包含完整的模型状态（包括量化器）
- 建议使用阶段3的模型进行转换

### 2. 维度要求
- 输入图像必须是3通道
- 图像尺寸需要与训练时一致
- 量化器维度必须匹配

### 3. 性能优化
- ONNX模型支持批处理
- 可以在GPU上运行（如果支持）
- 建议在目标平台上测试性能

### 4. 故障排除

**常见错误及解决方案:**

1. **维度不匹配错误**:
   ```
   ValueError: Input bits_after_channel dim X does not match DENSEQuantizer.code_dim Y
   ```
   - 检查`--quant_code_dim`参数是否与训练时一致

2. **模型加载失败**:
   ```
   KeyError: 'quantizer_state_dict'
   ```
   - 确保使用阶段2或阶段3的检查点文件

3. **ONNX验证失败**:
   - 检查PyTorch和ONNX版本兼容性
   - 尝试使用较低的opset版本

## 测试转换

使用提供的测试脚本验证转换功能：

```bash
python test_onnx_conversion.py
```

这个脚本会：
1. 创建一个测试模型
2. 执行ONNX转换
3. 验证生成的文件
4. 清理测试文件

## 版本要求

- PyTorch >= 1.13
- ONNX >= 1.12
- ONNXRuntime >= 1.12 (用于部署)

确保所有依赖版本兼容，以避免转换和部署问题。
