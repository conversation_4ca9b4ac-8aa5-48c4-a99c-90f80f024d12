# models/core_model.py
from random import choice

import torch
import torch.nn as nn

from loss.distortion import Distortion  # Assuming loss module is accessible

from .decoder import create_decoder  # Assuming decoder.py is in the same directory
from .encoder import create_encoder  # Assuming encoder.py is in the same directory
from .interleaver import BlockInterleaver

class CoreModel(nn.Module):
    """
    Combines an encoder and a decoder for end-to-end image reconstruction.
    This class does NOT include the channel simulation or quantization directly.
    It serves as a container for the autoencoder part of the WITT model.
    """

    def __init__(
        self, args_config, model_config
    ):  # Pass experiment args and model-specific config
        super(CoreModel, self).__init__()
        self.args_config = (
            args_config  # Contains experiment-level settings like SNR, model_type
        )
        self.model_config = (
            model_config  # Contains architecture details like embed_dims, depths
        )

        # Create encoder and decoder based on the model_config
        # model_config should have encoder_kwargs and decoder_kwargs
        # Pass use_adaptive_modulation based on args_config.model (new name for args.model_type)
        self.encoder = create_encoder(
            **model_config.encoder_kwargs,
            use_adaptive_modulation=(args_config.model == "WITT"),
        )
        self.decoder = create_decoder(
            **model_config.decoder_kwargs,
            use_adaptive_modulation=(args_config.model == "WITT"),
        )
        self.interleaver = BlockInterleaver()

        # Loss function for reconstruction quality
        self.distortion_loss_fn = Distortion(
            args_config
        )  # args_config should have distortion_metric
        self.mse_for_psnr = nn.MSELoss(
            reduction="mean"
        )  # For PSNR calculation, applied to [0,255] images

        # Resolution tracking for dynamic updates
        self.H_img = 0  # Input image height
        self.W_img = 0  # Input image width

        self.downsample_factor_encoder = (
            model_config.downsample_factor
        )  # e.g., 3 means 2^3=8x downsampling

        # SNR settings (parsed from args_config.multiple_snr)
        # args_config.multiple_snr should be the string e.g. "5,10,15"
        self.multiple_snr_list = [int(s) for s in args_config.multiple_snr.split(",")]

    def _update_resolutions(self, H_in, W_in):
        """Updates resolutions for encoder and decoder if input size changes."""
        if H_in != self.H_img or W_in != self.W_img:
            self.H_img = H_in
            self.W_img = W_in

            self.encoder.update_resolution(H_in, W_in)

            # Calculate feature map resolution after encoder
            # This depends on patch_size and number of downsampling stages in encoder
            # Example: if patch_size=2, and encoder has N stages with 2x downsampling each,
            # H_feat = H_in / (2 * 2^N)
            # A simpler way is to use the downsample_factor from config if it represents total spatial reduction
            H_feat = (
                H_in // (2**self.downsample_factor_encoder)
                if self.downsample_factor_encoder > 0
                else H_in
            )
            W_feat = (
                W_in // (2**self.downsample_factor_encoder)
                if self.downsample_factor_encoder > 0
                else W_in
            )

            # Ensure H_feat and W_feat are not zero if H_in/W_in were small
            H_feat = max(
                H_feat, 0
            )  # Or 1 if a minimum spatial dimension is required by decoder blocks
            W_feat = max(W_feat, 0)  # Or 1

            self.decoder.update_resolution(H_feat, W_feat)

    def encode(self, input_image: torch.Tensor, given_SNR: int = None) -> torch.Tensor:
        """
        Encodes the input image into a feature tensor.
        Args:
            input_image (torch.Tensor): Batch of input images (B, C, H, W).
            given_SNR (int, optional): Specific SNR value for ModNet. If None, a random one is chosen.
        Returns:
            torch.Tensor: Encoded features (B, L_feat, D_feat).
        """
        B, _, H, W = input_image.shape
        self._update_resolutions(H, W)

        snr_for_modnet = (
            given_SNR if given_SNR is not None else choice(self.multiple_snr_list)
        )

        features = self.encoder(input_image, snr_for_modnet, self.args_config.model)
        return features

    def decode(self, features: torch.Tensor, given_SNR: int = None) -> torch.Tensor:
        """
        Decodes features back into an image.
        Args:
            features (torch.Tensor): Encoded features (B, L_feat, D_feat).
            given_SNR (int, optional): Specific SNR value for ModNet. If None, a random one is chosen.
        Returns:
            torch.Tensor: Reconstructed images (B, C, H, W), typically in [0,1] range.
        """
        snr_for_modnet = (
            given_SNR if given_SNR is not None else choice(self.multiple_snr_list)
        )

        reconstructed_image = self.decoder(
            features, snr_for_modnet, self.args_config.model
        )
        return reconstructed_image.clamp(0.0, 1.0)

    def forward_reconstruction(self, input_image: torch.Tensor, given_SNR: int = None):
        """
        Full autoencoding pass: encode -> decode.
        Args:
            input_image (torch.Tensor): Batch of input images.
            given_SNR (int, optional): SNR for ModNet.
        Returns:
            torch.Tensor: Reconstructed images.
            torch.Tensor: Encoded features.
            int: SNR value used for ModNet.
        """
        snr_to_use = (
            given_SNR if given_SNR is not None else choice(self.multiple_snr_list)
        )

        features = self.encode(input_image, snr_to_use)
        reconstructed_image = self.decode(features, snr_to_use)

        return reconstructed_image, features, snr_to_use

    def calculate_psnr(
        self, img1: torch.Tensor, img2: torch.Tensor, data_range: float = 255.0
    ) -> torch.Tensor:
        """Calculates PSNR between two images. Assumes img1, img2 are in [0,1] range."""
        # Ensure inputs are float for mse calculation
        img1_float = img1.float()
        img2_float = img2.float()
        mse_val = self.mse_for_psnr(img1_float * data_range, img2_float * data_range)
        if mse_val == 0:
            return torch.tensor(
                float("inf"), device=img1.device
            )  # Or a large finite number like 100.0
        return 10 * torch.log10((data_range**2) / mse_val)

    def calculate_loss(
        self, original_image: torch.Tensor, reconstructed_image: torch.Tensor
    ) -> torch.Tensor:
        """Calculates the distortion loss."""
        return self.distortion_loss_fn(
            original_image, reconstructed_image.clamp(0.0, 1.0)
        )


if __name__ == "__main__":

    class DummyArgsConfig:
        model = "WITT"
        multiple_snr = "0,5,10,15"
        distortion_metric = "MSE"
        trainset = "celeba"

    class DummyModelConfig:
        image_dims = (3, 64, 64)
        downsample_factor = 3
        encoder_kwargs = dict(
            img_size=(64, 64),
            patch_size=2,
            in_chans=3,
            embed_dims=[128, 256, 512],
            depths=[2, 4, 4],
            num_heads=[2, 4, 8],
            C=96,  # C is for WITT_Encoder constructor
            window_size=2,
            mlp_ratio=4.0,
            qkv_bias=True,
            qk_scale=None,
            norm_layer=nn.LayerNorm,
            patch_norm=True,
        )
        decoder_kwargs = dict(
            img_size=(64, 64),
            embed_dims=[512, 256, 128],
            depths=[4, 4, 2],
            num_heads=[8, 4, 2],
            C=512,  # Input feature dim to decoder (output of encoder)
            window_size=2,
            mlp_ratio=4.0,
            qkv_bias=True,
            qk_scale=None,
            norm_layer=nn.LayerNorm,
        )

    args_conf = DummyArgsConfig()
    model_conf = DummyModelConfig()
    model_conf.decoder_kwargs["C"] = model_conf.encoder_kwargs["embed_dims"][-1]

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    core_model = CoreModel(args_conf, model_conf).to(device)

    dummy_input_image = torch.rand(2, 3, 64, 64).to(device)

    print("Testing CoreModel encoding...")
    features_out = core_model.encode(dummy_input_image, given_SNR=10)
    print(f"Encoded features shape: {features_out.shape}")

    print("\nTesting CoreModel decoding...")
    reconstructed_out = core_model.decode(features_out, given_SNR=10)
    print(f"Reconstructed image shape: {reconstructed_out.shape}")

    print("\nTesting CoreModel full forward pass...")
    recon_img_fwd, feats_fwd, snr_used_fwd = core_model.forward_reconstruction(
        dummy_input_image, given_SNR=5
    )
    print(
        f"Forward pass: Recon shape {recon_img_fwd.shape}, Feats shape {feats_fwd.shape}, SNR used {snr_used_fwd}"
    )

    print("\nTesting loss calculation...")
    loss = core_model.calculate_loss(dummy_input_image, reconstructed_out)
    print(f"Reconstruction loss ({args_conf.distortion_metric}): {loss.item()}")

    print("\nTesting PSNR calculation...")
    psnr = core_model.calculate_psnr(dummy_input_image, reconstructed_out)
    print(f"PSNR: {psnr.item()} dB")

    print("\nTesting with different image size (e.g., 32x32):")
    # Update model_conf for a different size if needed, or rely on dynamic update
    # For this test, let's assume the model can adapt.
    dummy_input_32 = torch.rand(1, 3, 32, 32).to(device)
    # core_model._update_resolutions(32,32) # Explicitly update if not done by encode
    recon_32, feats_32, _ = core_model.forward_reconstruction(dummy_input_32)
    print(
        f"Recon shape for 32x32 input: {recon_32.shape}, feats shape: {feats_32.shape}"
    )
    # Expected recon_32 shape (1,3,32,32) if decoder's target_H/W updates correctly.
    # Expected feats_32 shape (1, L_feat_32, D_feat)
