ONNX模型推理测试报告
==================================================

发送端模型: transmitter_model.onnx
接收端模型: receiver_model.onnx
输入图像1: D:/data/CelebA64/test/images/000004.jpg
输入图像2: D:/data/CelebA64/test/images/000017.jpg

推理时间:
  发送端: 0.0436秒
  接收端: 0.0782秒
  总计: 0.1217秒

质量指标:
  图像1 - MSE: 0.005051, PSNR: 22.97 dB
  图像2 - MSE: 0.005065, PSNR: 22.95 dB
  平均PSNR: 22.96 dB

输出文件:
  - original_image1.png: 原始图像1
  - original_image2.png: 原始图像2
  - reconstructed_image1.png: 重构图像1
  - reconstructed_image2.png: 重构图像2
  - comparison_image1.png: 对比图像1
  - comparison_image2.png: 对比图像2
