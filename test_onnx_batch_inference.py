#!/usr/bin/env python3
"""
ONNX模型批量推理测试脚本

支持一次读取多个图片进行批量测试，保存原始图片和重构图像进行对比
"""

import argparse
import glob
import os
import random
import sys
import time

import numpy as np
import onnxruntime as ort
import torchvision.transforms as transforms
from PIL import Image
from tqdm import tqdm


def setup_onnx_session(model_path, providers=None):
    """设置ONNX推理会话"""
    if providers is None:
        # 优先使用GPU，如果不可用则使用CPU
        providers = ["CUDAExecutionProvider", "CPUExecutionProvider"]

    try:
        session = ort.InferenceSession(model_path, providers=providers)
        print(f"成功加载模型: {model_path}")
        print(f"使用提供者: {session.get_providers()}")
        return session
    except Exception as e:
        print(f"加载模型失败: {e}")
        return None


def load_and_preprocess_image(image_path, target_size=(64, 64)):
    """加载和预处理图像"""
    try:
        # 加载图像
        image = Image.open(image_path).convert("RGB")

        # 预处理变换
        transform = transforms.Compose(
            [
                transforms.Resize(target_size),
                transforms.ToTensor(),  # 转换为[0,1]范围
            ]
        )

        # 应用变换并添加批次维度
        tensor = transform(image).unsqueeze(0)  # [1, 3, H, W]

        return tensor.numpy().astype(np.float32), image
    except Exception as e:
        print(f"加载图像失败 {image_path}: {e}")
        return None, None


def get_image_pairs_from_directory(image_dir, num_pairs=None, random_seed=42):
    """从目录中获取图像对"""
    # 支持的图像格式
    image_extensions = ["*.jpg", "*.jpeg", "*.png", "*.bmp", "*.tiff"]

    # 获取所有图像文件
    image_files = []
    for ext in image_extensions:
        image_files.extend(glob.glob(os.path.join(image_dir, ext)))
        image_files.extend(glob.glob(os.path.join(image_dir, ext.upper())))

    if len(image_files) == 0:
        print(f"在目录 {image_dir} 中没有找到图像文件")
        return []

    print(f"找到 {len(image_files)} 个图像文件")

    # 随机打乱
    if random_seed is not None:
        random.seed(random_seed)
        random.shuffle(image_files)

    # 创建图像对
    image_pairs = []
    for i in range(0, len(image_files) - 1, 2):
        image_pairs.append((image_files[i], image_files[i + 1]))

    # 限制数量
    if num_pairs is not None and num_pairs < len(image_pairs):
        image_pairs = image_pairs[:num_pairs]

    print(f"创建了 {len(image_pairs)} 个图像对")
    return image_pairs


def tensor_to_image(tensor):
    """将张量转换为PIL图像"""
    # 确保张量在[0,1]范围内
    tensor = np.clip(tensor, 0, 1)

    # 移除批次维度并转换为[H, W, C]
    if tensor.ndim == 4:
        tensor = tensor.squeeze(0)  # [3, H, W]
    tensor = np.transpose(tensor, (1, 2, 0))  # [H, W, 3]

    # 转换为[0,255]范围并转换为uint8
    tensor = (tensor * 255).astype(np.uint8)

    return Image.fromarray(tensor)


def calculate_metrics(original, reconstructed):
    """计算重构质量指标"""
    # 确保数据在相同范围内
    original = np.clip(original, 0, 1)
    reconstructed = np.clip(reconstructed, 0, 1)

    # 计算MSE
    mse = np.mean((original - reconstructed) ** 2)

    # 计算PSNR
    if mse == 0:
        psnr = float("inf")
    else:
        psnr = 20 * np.log10(1.0 / np.sqrt(mse))

    return mse, psnr


def create_comparison_grid(images, titles, output_path, grid_size=None):
    """创建图像网格对比图"""
    if len(images) != len(titles):
        raise ValueError("图像数量和标题数量不匹配")

    if grid_size is None:
        # 自动计算网格大小
        n = len(images)
        cols = int(np.ceil(np.sqrt(n)))
        rows = int(np.ceil(n / cols))
    else:
        rows, cols = grid_size

    # 获取单个图像的尺寸
    img_width, img_height = images[0].size

    # 创建网格图像
    grid_width = cols * img_width
    grid_height = rows * img_height
    grid_image = Image.new("RGB", (grid_width, grid_height), color="white")

    # 粘贴图像
    for i, (img, title) in enumerate(zip(images, titles)):
        if i >= rows * cols:
            break

        row = i // cols
        col = i % cols

        x = col * img_width
        y = row * img_height

        grid_image.paste(img, (x, y))

    # 保存网格图像
    grid_image.save(output_path)
    return grid_image


def test_batch_inference(
    transmitter_path, receiver_path, image_pairs, output_dir, save_individual=True
):
    """批量测试ONNX模型推理"""

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 加载ONNX模型
    print("加载ONNX模型...")
    transmitter_session = setup_onnx_session(transmitter_path)
    receiver_session = setup_onnx_session(receiver_path)

    if transmitter_session is None or receiver_session is None:
        print("模型加载失败!")
        return False

    # 统计信息
    total_pairs = len(image_pairs)
    total_transmitter_time = 0
    total_receiver_time = 0
    all_mse1, all_mse2 = [], []
    all_psnr1, all_psnr2 = [], []

    # 用于创建网格的图像列表
    grid_originals1, grid_originals2 = [], []
    grid_reconstructed1, grid_reconstructed2 = [], []

    print(f"开始批量推理测试，共 {total_pairs} 个图像对...")

    # 批量处理
    for pair_idx, (image1_path, image2_path) in enumerate(
        tqdm(image_pairs, desc="处理图像对")
    ):
        try:
            # 加载和预处理图像
            input1_tensor, original_img1 = load_and_preprocess_image(image1_path)
            input2_tensor, original_img2 = load_and_preprocess_image(image2_path)

            if input1_tensor is None or input2_tensor is None:
                print(f"跳过图像对 {pair_idx}: 加载失败")
                continue

            # 发送端推理
            start_time = time.time()
            transmitter_outputs = transmitter_session.run(
                None, {"input1": input1_tensor, "input2": input2_tensor}
            )

            # 发送端模型只返回交织特征
            if len(transmitter_outputs) == 1:
                interleaved_feature = transmitter_outputs[0]
            else:
                interleaved_feature = transmitter_outputs[0]

            transmitter_time = time.time() - start_time
            total_transmitter_time += transmitter_time

            # 接收端推理
            start_time = time.time()
            receiver_outputs = receiver_session.run(
                None, {"interleaved_feature": interleaved_feature}
            )
            output1, output2 = receiver_outputs

            receiver_time = time.time() - start_time
            total_receiver_time += receiver_time

            # 转换重构图像
            reconstructed_img1 = tensor_to_image(output1)
            reconstructed_img2 = tensor_to_image(output2)

            # 计算质量指标
            mse1, psnr1 = calculate_metrics(input1_tensor, output1)
            mse2, psnr2 = calculate_metrics(input2_tensor, output2)

            all_mse1.append(mse1)
            all_mse2.append(mse2)
            all_psnr1.append(psnr1)
            all_psnr2.append(psnr2)

            # 保存单独的图像文件（可选）
            if save_individual:
                pair_dir = os.path.join(output_dir, f"pair_{pair_idx:03d}")
                os.makedirs(pair_dir, exist_ok=True)

                original_img1.save(os.path.join(pair_dir, "original_1.png"))
                original_img2.save(os.path.join(pair_dir, "original_2.png"))
                reconstructed_img1.save(os.path.join(pair_dir, "reconstructed_1.png"))
                reconstructed_img2.save(os.path.join(pair_dir, "reconstructed_2.png"))

                # 创建对比图像
                create_comparison_image(
                    original_img1,
                    reconstructed_img1,
                    os.path.join(pair_dir, "comparison_1.png"),
                )
                create_comparison_image(
                    original_img2,
                    reconstructed_img2,
                    os.path.join(pair_dir, "comparison_2.png"),
                )

            # 收集用于网格显示的图像（只保存前16个）
            if len(grid_originals1) < 16:
                grid_originals1.append(original_img1)
                grid_originals2.append(original_img2)
                grid_reconstructed1.append(reconstructed_img1)
                grid_reconstructed2.append(reconstructed_img2)

        except Exception as e:
            print(f"处理图像对 {pair_idx} 时出错: {e}")
            continue

    # 创建网格对比图像
    if grid_originals1:
        print("创建网格对比图像...")
        create_comparison_grid(
            grid_originals1,
            [f"Original 1-{i}" for i in range(len(grid_originals1))],
            os.path.join(output_dir, "grid_originals_1.png"),
        )
        create_comparison_grid(
            grid_reconstructed1,
            [f"Reconstructed 1-{i}" for i in range(len(grid_reconstructed1))],
            os.path.join(output_dir, "grid_reconstructed_1.png"),
        )
        create_comparison_grid(
            grid_originals2,
            [f"Original 2-{i}" for i in range(len(grid_originals2))],
            os.path.join(output_dir, "grid_originals_2.png"),
        )
        create_comparison_grid(
            grid_reconstructed2,
            [f"Reconstructed 2-{i}" for i in range(len(grid_reconstructed2))],
            os.path.join(output_dir, "grid_reconstructed_2.png"),
        )

    # 计算统计信息
    processed_pairs = len(all_mse1)
    if processed_pairs == 0:
        print("没有成功处理任何图像对")
        return False

    avg_mse1 = np.mean(all_mse1)
    avg_mse2 = np.mean(all_mse2)
    avg_psnr1 = np.mean(all_psnr1)
    avg_psnr2 = np.mean(all_psnr2)

    std_psnr1 = np.std(all_psnr1)
    std_psnr2 = np.std(all_psnr2)

    avg_transmitter_time = total_transmitter_time / processed_pairs
    avg_receiver_time = total_receiver_time / processed_pairs
    total_time = total_transmitter_time + total_receiver_time

    # 保存详细报告
    report_path = os.path.join(output_dir, "batch_inference_report.txt")
    with open(report_path, "w", encoding="utf-8") as f:
        f.write("ONNX模型批量推理测试报告\n")
        f.write("=" * 60 + "\n\n")
        f.write(f"发送端模型: {transmitter_path}\n")
        f.write(f"接收端模型: {receiver_path}\n")
        f.write(f"处理的图像对数量: {processed_pairs} / {total_pairs}\n\n")

        f.write("推理时间统计:\n")
        f.write(f"  平均发送端时间: {avg_transmitter_time:.4f}秒\n")
        f.write(f"  平均接收端时间: {avg_receiver_time:.4f}秒\n")
        f.write(f"  平均总时间: {avg_transmitter_time + avg_receiver_time:.4f}秒\n")
        f.write(f"  总处理时间: {total_time:.4f}秒\n")
        f.write(f"  吞吐量: {processed_pairs / total_time:.2f} 图像对/秒\n\n")

        f.write("质量指标统计:\n")
        f.write(f"  图像1 - 平均MSE: {avg_mse1:.6f} ± {np.std(all_mse1):.6f}\n")
        f.write(f"  图像1 - 平均PSNR: {avg_psnr1:.2f} ± {std_psnr1:.2f} dB\n")
        f.write(f"  图像2 - 平均MSE: {avg_mse2:.6f} ± {np.std(all_mse2):.6f}\n")
        f.write(f"  图像2 - 平均PSNR: {avg_psnr2:.2f} ± {std_psnr2:.2f} dB\n")
        f.write(f"  总体平均PSNR: {(avg_psnr1 + avg_psnr2) / 2:.2f} dB\n\n")

        f.write("PSNR分布:\n")
        f.write(f"  图像1 - 最小值: {np.min(all_psnr1):.2f} dB\n")
        f.write(f"  图像1 - 最大值: {np.max(all_psnr1):.2f} dB\n")
        f.write(f"  图像2 - 最小值: {np.min(all_psnr2):.2f} dB\n")
        f.write(f"  图像2 - 最大值: {np.max(all_psnr2):.2f} dB\n\n")

        f.write("输出文件:\n")
        if save_individual:
            f.write("  - pair_XXX/: 每个图像对的详细结果\n")
        f.write("  - grid_originals_1.png: 原始图像1网格\n")
        f.write("  - grid_reconstructed_1.png: 重构图像1网格\n")
        f.write("  - grid_originals_2.png: 原始图像2网格\n")
        f.write("  - grid_reconstructed_2.png: 重构图像2网格\n")

    # 打印总结
    print("\n批量推理测试完成!")
    print(f"处理的图像对: {processed_pairs} / {total_pairs}")
    print(
        f"平均PSNR: 图像1={avg_psnr1:.2f}±{std_psnr1:.2f} dB, 图像2={avg_psnr2:.2f}±{std_psnr2:.2f} dB"
    )
    print(f"总体平均PSNR: {(avg_psnr1 + avg_psnr2) / 2:.2f} dB")
    print(f"平均推理时间: {avg_transmitter_time + avg_receiver_time:.4f}秒/对")
    print(f"吞吐量: {processed_pairs / total_time:.2f} 图像对/秒")
    print(f"结果保存在: {output_dir}")

    return True


def create_comparison_image(original, reconstructed, output_path):
    """创建原始图像和重构图像的对比图"""
    # 确保两个图像大小相同
    if original.size != reconstructed.size:
        reconstructed = reconstructed.resize(original.size, Image.LANCZOS)

    # 创建并排对比图像
    width, height = original.size
    comparison = Image.new("RGB", (width * 2, height))

    # 粘贴图像
    comparison.paste(original, (0, 0))
    comparison.paste(reconstructed, (width, 0))

    # 保存对比图像
    comparison.save(output_path)


def main():
    parser = argparse.ArgumentParser(description="ONNX模型批量推理测试")

    # 模型路径
    parser.add_argument(
        "--transmitter_model",
        type=str,
        default="transmitter_model.onnx",
        help="发送端ONNX模型路径",
    )
    parser.add_argument(
        "--receiver_model",
        type=str,
        default="receiver_model.onnx",
        help="接收端ONNX模型路径",
    )

    # 输入图像设置
    parser.add_argument("--image_dir", type=str, required=True, help="图像目录路径")
    parser.add_argument(
        "--num_pairs", type=int, default=None, help="要处理的图像对数量（默认处理所有）"
    )
    parser.add_argument("--random_seed", type=int, default=42, help="随机种子")

    # 输出设置
    parser.add_argument(
        "--output_dir", type=str, default="./batch_inference_results", help="输出目录"
    )
    parser.add_argument(
        "--save_individual", action="store_true", help="保存每个图像对的单独结果"
    )

    # 模型设置
    parser.add_argument(
        "--image_size",
        type=int,
        nargs=2,
        default=[64, 64],
        help="图像尺寸 [高度, 宽度]",
    )

    args = parser.parse_args()

    print("ONNX模型批量推理测试")
    print("=" * 60)

    # 检查模型文件是否存在
    if not os.path.exists(args.transmitter_model):
        print(f"发送端模型文件不存在: {args.transmitter_model}")
        print("请先运行 convert_to_onnx.py 生成ONNX模型")
        return 1

    if not os.path.exists(args.receiver_model):
        print(f"接收端模型文件不存在: {args.receiver_model}")
        print("请先运行 convert_to_onnx.py 生成ONNX模型")
        return 1

    # 检查图像目录
    if not os.path.exists(args.image_dir):
        print(f"图像目录不存在: {args.image_dir}")
        return 1

    # 获取图像对
    print(f"从目录读取图像: {args.image_dir}")
    image_pairs = get_image_pairs_from_directory(
        args.image_dir, num_pairs=args.num_pairs, random_seed=args.random_seed
    )

    if len(image_pairs) == 0:
        print("没有找到可用的图像对")
        return 1

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    # 执行批量推理测试
    success = test_batch_inference(
        args.transmitter_model,
        args.receiver_model,
        image_pairs,
        args.output_dir,
        save_individual=args.save_individual,
    )

    if success:
        print("\n批量测试完成!")
        print(f"请查看输出目录: {args.output_dir}")
        print("\n推荐查看:")
        print(f"  - {args.output_dir}/batch_inference_report.txt - 详细统计报告")
        print(f"  - {args.output_dir}/grid_*.png - 网格对比图像")
        if args.save_individual:
            print(f"  - {args.output_dir}/pair_XXX/ - 每个图像对的详细结果")
        return 0
    else:
        print("\n批量测试失败!")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
