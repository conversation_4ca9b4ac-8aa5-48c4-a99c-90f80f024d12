import torch


class BlockInterleaver(torch.nn.Module):
    """
    交换batch和channel维度的交织器
    例如：将形状为 [batch_size, channels, *dims] 的tensor重排为 [channels, batch_size, *dims]
    确保交换后的第一维大小与原始batch大小相同
    """

    def __init__(self):
        super(BlockInterleaver, self).__init__()

    def interleave(self, x):
        """
        交换batch和channel维度
        Args:
            x: Input tensor of shape [batch_size, channels, *dims]
        Returns:
            interleaved tensor and metadata
        """
        orig_shape = x.shape
        batch_size = orig_shape[0]
        
        # 交换batch和channel维度
        x_interleaved = x.permute(1, 0, *range(2, len(orig_shape)))
        x_interleaved_shape = x_interleaved.shape
        x_interleaved = x_interleaved.reshape(batch_size, -1)

        # 记录原始形状信息
        metadata = {
            "x_interleaved_shape": x_interleaved_shape,
        }
 
        return x_interleaved, metadata

    def deinterleave(self, x, metadata):
        """
        将交换维度后的tensor恢复到原始维度顺序
        Args:
            x: Interleaved tensor
            metadata: Dictionary containing original shape information
        """
        orig_shape = metadata["x_interleaved_shape"]

        x_deinterleaved = x.reshape(orig_shape)

        # 交换回原始维度顺序
        x_deinterleaved = x_deinterleaved.permute(1, 0, *range(2, len(orig_shape)))
        
        return x_deinterleaved

    def forward(self, x, mode="interleave", metadata=None):
        """
        Forward pass
        Args:
            x: Input tensor
            mode: 'interleave' or 'deinterleave'
            metadata: For deinterleave, this contains the original shape information
        """
        if mode == "interleave":
            return self.interleave(x)
        else:  # deinterleave
            return self.deinterleave(x, metadata)
