# models/modules.py
import torch
import torch.nn as nn
import torch.nn.functional as F
from timm.models.layers import DropPath, to_2tuple, trunc_normal_


class Mlp(nn.Module):
    """Multilayer Perceptron."""

    def __init__(
        self,
        in_features,
        hidden_features=None,
        out_features=None,
        act_layer=nn.GELU,
        drop=0.0,
    ):
        super().__init__()
        out_features = out_features or in_features
        hidden_features = hidden_features or in_features
        self.fc1 = nn.Linear(in_features, hidden_features)
        self.act = act_layer()
        self.fc2 = nn.Linear(hidden_features, out_features)
        self.drop = nn.Dropout(drop)

    def forward(self, x):
        x = self.fc1(x)
        x = self.act(x)
        x = self.drop(x)  # Dropout after activation in fc1
        x = self.fc2(x)
        x = self.drop(x)  # Dropout after fc2
        return x


def window_partition(x, window_size: int):
    """
    Partition tensor into non-overlapping windows.
    Args:
        x: (B, H, W, C)
        window_size (int): Window size.
    Returns:
        windows: (num_windows*B, window_size, window_size, C)
    """
    B, H, W, C = x.shape

    if H == 0 or W == 0 or window_size == 0:
        return torch.empty((0, 0, 0, C), device=x.device, dtype=x.dtype)

    if H < window_size or W < window_size:
        # This implies SwinBlock should have adjusted window_size to min(H,W)
        # If this happens, it means window_size is effectively H or W.
        # The view below should still work if H/W are multiples of the *adjusted* window_size.
        # SwinBlock._adjust_window_shift_size handles this.
        pass

    # Ensure H and W are divisible by window_size for the view operation.
    # This should be guaranteed by SwinBlock's _adjust_window_shift_size if H,W > 0.
    if H % window_size != 0 or W % window_size != 0:
        # This is an unexpected state if SwinBlock logic is correct.
        # Fallback or error. For now, assume this won't be hit if H,W,window_size > 0.
        # print(f"Warning: window_partition H({H}) or W({W}) not divisible by window_size({window_size}). This might lead to errors.")
        # To prevent view error, one might pad x here, or ensure SwinBlock always makes it divisible.
        # For simplicity, assuming SwinBlock ensures divisibility or adjusted window_size.
        pass

    x = x.view(B, H // window_size, window_size, W // window_size, window_size, C)
    windows = (
        x.permute(0, 1, 3, 2, 4, 5).contiguous().view(-1, window_size, window_size, C)
    )
    return windows


def window_reverse(windows, window_size: int, H: int, W: int):
    """
    Merge windows back to original tensor shape.
    Args:
        windows: (num_windows*B, window_size, window_size, C)
        window_size (int): Window size.
        H (int): Height of image.
        W (int): Width of image.
    Returns:
        x: (B, H, W, C)
    """
    num_channels = windows.shape[-1] if windows.numel() > 0 else 0
    if H == 0 or W == 0 or window_size == 0:
        batch_size_guess = (
            windows.shape[0] if windows.numel() > 0 and window_size == 0 else 0
        )  # A guess for B
        if windows.numel() == 0:
            batch_size_guess = 0
        return torch.empty(
            (batch_size_guess, H, W, num_channels),
            device=windows.device,
            dtype=windows.dtype,
        )

    num_windows_h = H // window_size
    num_windows_w = W // window_size

    if num_windows_h * num_windows_w == 0:
        if windows.numel() > 0:
            # This implies H or W < window_size, but SwinBlock should have adjusted window_size.
            # If this is hit, it's an inconsistency.
            # print(f"Warning: window_reverse inconsistency. H={H},W={W},ws={window_size}. num_windows_h/w is 0 but windows exist.")
            # Fallback: try to determine B, but view will fail.
            B = windows.shape[0]  # This is a guess.
            return torch.empty(
                (B, H, W, num_channels), device=windows.device, dtype=windows.dtype
            )
        else:
            B = 0
    else:
        B = int(windows.shape[0] / (num_windows_h * num_windows_w))

    x = windows.view(B, num_windows_h, num_windows_w, window_size, window_size, -1)
    x = x.permute(0, 1, 3, 2, 4, 5).contiguous().view(B, H, W, -1)
    return x


class WindowAttention(nn.Module):
    r"""Window based multi-head self attention (W-MSA) module with relative position bias."""

    def __init__(
        self,
        dim,
        window_size,
        num_heads,
        qkv_bias=True,
        qk_scale=None,
        attn_drop=0.0,
        proj_drop=0.0,
    ):
        super().__init__()
        self.dim = dim
        self.window_size = window_size  # Wh, Ww (tuple)
        self.num_heads = num_heads
        head_dim = dim // num_heads
        if dim % num_heads != 0:
            raise ValueError(f"dim {dim} must be divisible by num_heads {num_heads}.")
        self.scale = qk_scale or head_dim**-0.5

        self.relative_position_bias_table = nn.Parameter(
            torch.zeros(
                (2 * self.window_size[0] - 1) * (2 * self.window_size[1] - 1), num_heads
            )
        )

        coords_h = torch.arange(self.window_size[0])
        coords_w = torch.arange(self.window_size[1])
        coords = torch.stack(torch.meshgrid([coords_h, coords_w], indexing="ij"))
        coords_flatten = torch.flatten(coords, 1)
        relative_coords = coords_flatten[:, :, None] - coords_flatten[:, None, :]
        relative_coords = relative_coords.permute(1, 2, 0).contiguous()
        relative_coords[:, :, 0] += self.window_size[0] - 1
        relative_coords[:, :, 1] += self.window_size[1] - 1
        relative_coords[:, :, 0] *= 2 * self.window_size[1] - 1
        relative_position_index = relative_coords.sum(-1)
        self.register_buffer(
            "relative_position_index", relative_position_index, persistent=False
        )

        self.qkv = nn.Linear(dim, dim * 3, bias=qkv_bias)
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)

        trunc_normal_(self.relative_position_bias_table, std=0.02)
        self.softmax = nn.Softmax(dim=-1)

    def forward(self, x, mask=None):
        B_, N, C = x.shape  # N = window_size[0] * window_size[1]

        if N == 0:  # If the window is empty
            return torch.zeros_like(x)

        qkv = (
            self.qkv(x)
            .reshape(B_, N, 3, self.num_heads, C // self.num_heads)
            .permute(2, 0, 3, 1, 4)
        )
        q, k, v = qkv[0], qkv[1], qkv[2]

        q = q * self.scale
        attn = q @ k.transpose(-2, -1)

        relative_position_bias = self.relative_position_bias_table[
            self.relative_position_index.view(-1)
        ].view(N, N, -1)
        relative_position_bias = relative_position_bias.permute(2, 0, 1).contiguous()
        attn = attn + relative_position_bias.unsqueeze(0)

        if mask is not None:
            nW = mask.shape[0]
            if nW == 0 or B_ == 0 or (B_ // nW == 0 and B_ > 0):
                pass
            else:
                attn = attn.view(B_ // nW, nW, self.num_heads, N, N) + mask.unsqueeze(
                    0
                ).unsqueeze(2)
                attn = attn.view(-1, self.num_heads, N, N)

            attn = self.softmax(attn)
        else:
            attn = self.softmax(attn)

        attn = self.attn_drop(attn)

        x = (attn @ v).transpose(1, 2).reshape(B_, N, C)
        x = self.proj(x)
        x = self.proj_drop(x)
        return x

    def extra_repr(self) -> str:
        return f"dim={self.dim}, window_size={self.window_size}, num_heads={self.num_heads}"

    def flops(self, N):
        if N == 0:
            return 0
        flops = 0
        flops += N * self.dim * (self.dim * 3)
        flops += self.num_heads * N * (self.dim // self.num_heads) * N
        flops += self.num_heads * N * N * (self.dim // self.num_heads)
        flops += N * self.dim * self.dim
        return flops


class SwinTransformerBlock(nn.Module):
    r"""Swin Transformer Block."""

    def __init__(
        self,
        dim,
        input_resolution,
        num_heads,
        window_size=7,
        shift_size=0,
        mlp_ratio=4.0,
        qkv_bias=True,
        qk_scale=None,
        act_layer=nn.GELU,
        norm_layer=nn.LayerNorm,
        drop_path=0.0,
    ):
        super().__init__()
        self.dim = dim
        self.input_resolution = input_resolution
        self.num_heads = num_heads

        self._original_window_size = window_size
        self._original_shift_size = shift_size
        self.window_size = window_size  # This will be int
        self.shift_size = shift_size  # This will be int

        self.mlp_ratio = mlp_ratio
        self._adjust_window_shift_size()

        self.norm1 = norm_layer(dim)

        # WindowAttention expects window_size as a tuple (Wh, Ww)
        # self.window_size is an int here. to_2tuple handles it.
        self.attn = WindowAttention(
            dim,
            window_size=to_2tuple(self.window_size),
            num_heads=num_heads,
            qkv_bias=qkv_bias,
            qk_scale=qk_scale,
        )

        self.drop_path = DropPath(drop_path) if drop_path > 0.0 else nn.Identity()
        self.norm2 = norm_layer(dim)
        mlp_hidden_dim = int(dim * mlp_ratio)
        self.mlp = Mlp(
            in_features=dim, hidden_features=mlp_hidden_dim, act_layer=act_layer
        )

        if self.shift_size > 0 and self.window_size > 0:
            self._make_attn_mask()
        else:
            self.register_buffer("attn_mask_buffer", None, persistent=False)

    def _adjust_window_shift_size(self):
        current_H, current_W = self.input_resolution
        # Reset to original configured sizes before adjustment for current resolution
        self.window_size = self._original_window_size
        self.shift_size = self._original_shift_size

        if current_H == 0 or current_W == 0:
            self.window_size = 0  # Make window_size int
            self.shift_size = 0
            return

        min_res = min(current_H, current_W)
        if min_res == 0:
            self.window_size = 0
            self.shift_size = 0
            return

        if min_res <= self.window_size:
            self.window_size = min_res
            self.shift_size = 0

        # Ensure shift_size is valid with potentially adjusted window_size
        if not (0 <= self.shift_size < self.window_size):
            if self.window_size > 0:
                self.shift_size = 0
            elif self.shift_size != 0:  # If window_size is 0, shift_size must be 0
                self.shift_size = 0

    def _make_attn_mask(self):
        H, W = self.input_resolution
        if H == 0 or W == 0 or self.window_size == 0:
            if hasattr(self, "attn_mask_buffer"):
                self.attn_mask_buffer = None
            else:
                self.register_buffer("attn_mask_buffer", None, persistent=False)
            return

        device = (
            self.norm1.weight.device
            if hasattr(self.norm1, "weight") and self.norm1.weight is not None
            else torch.device("cpu")
        )
        img_mask = torch.zeros((1, H, W, 1), device=device)

        h_slices = (
            slice(0, -self.window_size),
            slice(-self.window_size, -self.shift_size),
            slice(-self.shift_size, None),
        )
        w_slices = (
            slice(0, -self.window_size),
            slice(-self.window_size, -self.shift_size),
            slice(-self.shift_size, None),
        )
        cnt = 0
        for h in h_slices:
            for w in w_slices:
                img_mask[:, h, w, :] = cnt
                cnt += 1

        mask_windows = window_partition(
            img_mask, self.window_size
        )  # window_size is int here
        mask_windows = mask_windows.view(-1, self.window_size * self.window_size)
        attn_mask = mask_windows.unsqueeze(1) - mask_windows.unsqueeze(2)
        attn_mask = attn_mask.masked_fill(attn_mask != 0, float(-100.0)).masked_fill(
            attn_mask == 0, float(0.0)
        )

        if hasattr(self, "attn_mask_buffer"):
            del self.attn_mask_buffer
        self.register_buffer("attn_mask_buffer", attn_mask, persistent=False)

    def forward(self, x):
        H, W = self.input_resolution
        B, L, C = x.shape

        if H * W == 0:
            if L == 0:
                return x
            if L > 0:
                shortcut = x
                x_norm1 = self.norm1(x)
                x_attention_out = x_norm1
                x = shortcut + self.drop_path(x_attention_out)
                x = x + self.drop_path(self.mlp(self.norm2(x)))
                return x

        if L != H * W:
            raise ValueError(
                f"Input feature has wrong size, L={L}, H*W={H * W}, H={H}, W={W}"
            )

        shortcut = x
        x_norm1 = self.norm1(x)
        x_norm1_view = x_norm1.view(B, H, W, C)

        if self.shift_size > 0 and self.window_size > 0:
            shifted_x = torch.roll(
                x_norm1_view, shifts=(-self.shift_size, -self.shift_size), dims=(1, 2)
            )
        else:
            shifted_x = x_norm1_view

        if self.window_size > 0:
            x_windows = window_partition(
                shifted_x, self.window_size
            )  # window_size is int
            x_windows = x_windows.view(-1, self.window_size * self.window_size, C)

            current_mask = self.attn_mask_buffer
            if current_mask is not None and current_mask.device != x_windows.device:
                current_mask = current_mask.to(x_windows.device)

            attn_windows = self.attn(x_windows, mask=current_mask)
            attn_windows_merged = attn_windows.view(
                -1, self.window_size, self.window_size, C
            )
            shifted_x_merged = window_reverse(
                attn_windows_merged, self.window_size, H, W
            )  # window_size is int
        else:
            shifted_x_merged = shifted_x

        if self.shift_size > 0 and self.window_size > 0:
            x_attention_out = torch.roll(
                shifted_x_merged, shifts=(self.shift_size, self.shift_size), dims=(1, 2)
            )
        else:
            x_attention_out = shifted_x_merged

        x_attention_out = x_attention_out.view(B, H * W, C)

        x = shortcut + self.drop_path(x_attention_out)
        x = x + self.drop_path(self.mlp(self.norm2(x)))

        return x

    def update_resolution(self, H_new, W_new):
        self.input_resolution = (H_new, W_new)
        self._adjust_window_shift_size()

        if self.shift_size > 0 and self.window_size > 0:
            self._make_attn_mask()
        else:
            if hasattr(self, "attn_mask_buffer"):
                self.attn_mask_buffer = None
            elif not hasattr(self, "attn_mask_buffer"):  # Ensure it exists if accessed
                self.register_buffer("attn_mask_buffer", None, persistent=False)


class PatchMerging(nn.Module):
    r"""Patch Merging Layer."""

    def __init__(self, input_resolution, dim, out_dim=None, norm_layer=nn.LayerNorm):
        super().__init__()
        self.input_resolution = input_resolution
        self.dim = dim
        self.out_dim = out_dim if out_dim is not None else 2 * dim

        self.norm = norm_layer(4 * dim)
        self.reduction = nn.Linear(4 * dim, self.out_dim, bias=False)

    def forward(self, x):
        H, W = self.input_resolution
        B, L, C = x.shape

        if H == 0 or W == 0:
            return torch.zeros(B, 0, self.out_dim, device=x.device, dtype=x.dtype)

        if L != H * W:
            raise ValueError(f"Input feature has wrong size: L={L}, H*W={H * W}")

        if H < 2 or W < 2:
            if not hasattr(self, "_passthrough_projection_pm"):
                self._passthrough_norm_pm = nn.LayerNorm(self.dim).to(x.device)
                self._passthrough_projection_pm = nn.Linear(
                    self.dim, self.out_dim, bias=False
                ).to(x.device)
            x_normed = self._passthrough_norm_pm(x)
            return self._passthrough_projection_pm(x_normed)

        pad_h = (2 - H % 2) % 2
        pad_w = (2 - W % 2) % 2

        x_reshaped = x.view(B, H, W, C)
        if pad_h > 0 or pad_w > 0:
            x_reshaped = F.pad(x_reshaped, (0, 0, 0, pad_w, 0, pad_h))

        H_padded, W_padded = x_reshaped.shape[1], x_reshaped.shape[2]

        x0 = x_reshaped[:, 0::2, 0::2, :]
        x1 = x_reshaped[:, 1::2, 0::2, :]
        x2 = x_reshaped[:, 0::2, 1::2, :]
        x3 = x_reshaped[:, 1::2, 1::2, :]
        x_cat = torch.cat([x0, x1, x2, x3], -1)

        x_cat = x_cat.view(B, -1, 4 * C)

        x_cat = self.norm(x_cat)
        x_merged = self.reduction(x_cat)
        return x_merged

    def extra_repr(self) -> str:
        return f"input_resolution={self.input_resolution}, dim={self.dim}, out_dim={self.out_dim}"

    def flops(self):
        H, W = self.input_resolution
        if H < 2 or W < 2:
            if self.dim != self.out_dim:
                return H * W * self.dim + H * W * self.dim * self.out_dim
            return H * W * self.dim

        H_eff, W_eff = H + (2 - H % 2) % 2, W + (2 - W % 2) % 2
        flops = (H_eff // 2) * (W_eff // 2) * (4 * self.dim)
        flops += (H_eff // 2) * (W_eff // 2) * (4 * self.dim) * self.out_dim
        return flops


class PatchReverseMerging(nn.Module):
    r"""Patch Reverse Merging Layer (Upsampling)."""

    def __init__(self, input_resolution, dim, out_dim, norm_layer=nn.LayerNorm):
        super().__init__()
        self.input_resolution = input_resolution
        self.dim = dim
        self.out_dim = out_dim

        self.expansion = nn.Linear(dim, out_dim * 4, bias=False)
        self.norm = norm_layer(dim)
        self.pixel_shuffle = nn.PixelShuffle(2)

    def forward(self, x):
        H_in, W_in = self.input_resolution
        B, L, C_in = x.shape

        if H_in == 0 or W_in == 0:
            return torch.zeros(B, 0, self.out_dim, device=x.device, dtype=x.dtype)

        if L != H_in * W_in:
            raise ValueError(
                f"Input feature has wrong size: L={L}, H_in*W_in={H_in * W_in}"
            )

        x = self.norm(x)
        x_expanded = self.expansion(x)

        x_reshaped = (
            x_expanded.view(B, H_in, W_in, self.out_dim * 4)
            .permute(0, 3, 1, 2)
            .contiguous()
        )

        x_shuffled = self.pixel_shuffle(x_reshaped)

        x_flat = x_shuffled.flatten(2).transpose(1, 2).contiguous()
        return x_flat

    def extra_repr(self) -> str:
        return f"input_resolution={self.input_resolution}, dim={self.dim}, out_dim={self.out_dim}"

    def flops(self):
        H_in, W_in = self.input_resolution
        if H_in == 0 or W_in == 0:
            return 0
        flops = H_in * W_in * self.dim
        flops += H_in * W_in * self.dim * (self.out_dim * 4)
        return flops


class PatchEmbed(nn.Module):
    """Image to Patch Embedding."""

    def __init__(
        self,
        img_size=(224, 224),
        patch_size=4,
        in_chans=3,
        embed_dim=96,
        norm_layer=None,
    ):
        super().__init__()
        self.img_size_config = to_2tuple(img_size)
        self.patch_size_val = patch_size
        self.patch_size_tuple = to_2tuple(patch_size)

        self.in_chans = in_chans
        self.embed_dim = embed_dim

        if self.patch_size_tuple[0] == 0 or self.patch_size_tuple[1] == 0:
            self.proj = nn.Identity()
        else:
            self.proj = nn.Conv2d(
                in_chans,
                embed_dim,
                kernel_size=self.patch_size_tuple,
                stride=self.patch_size_tuple,
            )

        if norm_layer is not None:
            self.norm = norm_layer(embed_dim)
        else:
            self.norm = None

        self._update_patches_resolution(
            self.img_size_config[0], self.img_size_config[1]
        )

    def _update_patches_resolution(self, H_current, W_current):
        pH, pW = self.patch_size_tuple
        if pH == 0 or pW == 0:
            # If patch size is zero, effectively no patching occurs. Resolution is H_current, W_current.
            # Number of "patches" would be H_current * W_current if each pixel is a patch.
            # Or, if it means no output, then 0,0.
            # For Conv2d with kernel=0, it's ill-defined.
            # Let's assume if patch_size is 0, it means no patching, so num_patches = H*W, res = H,W
            # This is inconsistent with Conv2d. A linear projection might be better if patch_size=0.
            # For now, if patch_size is 0, this implies an issue.
            self.patches_resolution = [
                H_current,
                W_current,
            ]  # Effectively, no downsampling by patching
        else:
            # Effective H, W after considering padding for Conv2d stride
            # If H_current=0, H_eff=0.
            H_eff = (
                H_current + (pH - H_current % pH) % pH
                if H_current > 0 and pH > 0
                else H_current
            )
            W_eff = (
                W_current + (pW - W_current % pW) % pW
                if W_current > 0 and pW > 0
                else W_current
            )
            self.patches_resolution = [
                H_eff // pH if pH > 0 else H_eff,
                W_eff // pW if pW > 0 else W_eff,
            ]

        self.num_patches = self.patches_resolution[0] * self.patches_resolution[1]

    def forward(self, x):
        B, C, H, W = x.shape
        self._update_patches_resolution(H, W)

        if (
            self.patch_size_tuple[0] == 0
            or self.patch_size_tuple[1] == 0
            or H == 0
            or W == 0
        ):
            if isinstance(
                self.proj, nn.Identity
            ):  # If proj is Identity due to zero patch size
                # Reshape to (B, H*W, C) and project to embed_dim if needed
                if not hasattr(self, "_fallback_linear_proj"):
                    self._fallback_linear_proj = nn.Linear(
                        self.in_chans, self.embed_dim
                    ).to(x.device)
                x_flat = x.flatten(2).transpose(1, 2)  # B, H*W, C_in
                x_projected = self._fallback_linear_proj(x_flat)  # B, H*W, embed_dim
                if self.norm:
                    x_projected = self.norm(x_projected)
                return x_projected
            return torch.zeros(B, 0, self.embed_dim, device=x.device, dtype=x.dtype)

        # Pad if H, W are not perfectly divisible by patch_size, to ensure Conv2d covers full image.
        # Or if H,W < patch_size, pad to patch_size.
        pad_h_final, pad_w_final = 0, 0
        if H < self.patch_size_tuple[0]:
            pad_h_final = self.patch_size_tuple[0] - H
        elif H % self.patch_size_tuple[0] != 0:
            pad_h_final = self.patch_size_tuple[0] - (H % self.patch_size_tuple[0])

        if W < self.patch_size_tuple[1]:
            pad_w_final = self.patch_size_tuple[1] - W
        elif W % self.patch_size_tuple[1] != 0:
            pad_w_final = self.patch_size_tuple[1] - (W % self.patch_size_tuple[1])

        if pad_h_final > 0 or pad_w_final > 0:
            # F.pad format: (pad_left, pad_right, pad_top, pad_bottom)
            x = F.pad(x, (0, pad_w_final, 0, pad_h_final))

        x = self.proj(x)
        x = x.flatten(2)
        x = x.transpose(1, 2)

        if self.norm is not None:
            x = self.norm(x)
        return x

    def flops(self):
        Ho, Wo = self.patches_resolution
        if (
            Ho == 0
            or Wo == 0
            or self.patch_size_tuple[0] == 0
            or self.patch_size_tuple[1] == 0
        ):
            if isinstance(self.proj, nn.Identity) and hasattr(
                self, "_fallback_linear_proj"
            ):
                # Approx FLOPs for fallback: H*W*in_chans*embed_dim (Linear) + H*W*embed_dim (Norm)
                # Using img_size_config as H,W for this estimate.
                H_cfg, W_cfg = self.img_size_config
                flops = H_cfg * W_cfg * self.in_chans * self.embed_dim
                if self.norm:
                    flops += H_cfg * W_cfg * self.embed_dim
                return flops
            return 0

        flops = (
            Ho
            * Wo
            * self.embed_dim
            * self.in_chans
            * (self.patch_size_tuple[0] * self.patch_size_tuple[1])
        )
        if self.norm is not None:
            flops += Ho * Wo * self.embed_dim
        return flops
