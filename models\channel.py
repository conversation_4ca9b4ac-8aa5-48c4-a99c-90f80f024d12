# models/channel.py
import torch
import torch.nn as nn
import numpy as np
import math # For math.sqrt if needed, though np.sqrt is often used with PyTorch tensors via conversion

class Channel(nn.Module):
    """
    Simulates a communication channel.
    This version is based on 'Reconstruction原代码/net/channel.py', which takes
    one input feature tensor and simulates it passing through two paths (h1, h2)
    to produce two noisy output versions. This is suitable for a one-transmitter,
    two-receiver/path scenario, or for creating two distinct noisy versions of a
    single source's features for two decoders.

    The original script `train_CelebA.py` used this channel with a combined feature
    (feature = feature1 + feature2) as input.
    """

    def __init__(self, args_config, main_config): # args_config for channel_type, main_config for device, logger
        super(Channel, self).__init__()
        self.channel_type = args_config.channel_type # 'awgn' or 'rayleigh'
        self.device = main_config.device
        # self.h_fixed_example = torch.sqrt(torch.randn(1)**2 + torch.randn(1)**2) / 1.414 # Original, seems unused
        
        if hasattr(main_config, 'logger') and main_config.logger: # Check if logger exists
            main_config.logger.info(f'[Channel]: Built {self.channel_type} channel, SNR from {args_config.multiple_snr} dB.')
        else:
            print(f'[Channel]: Built {self.channel_type} channel, SNR from {args_config.multiple_snr} dB. (Logger not available)')


    def _complex_normalize_single_input(self, x: torch.Tensor, target_power: float = 1.0):
        """Normalizes a real-valued feature tensor to have a specific average complex power."""
        # x is assumed to be [Batch, FlatRealFeatures]
        # where FlatRealFeatures = L_complex_elements * 2 (real and imag parts concatenated)
        
        # Calculate current average power of each element squared
        current_element_avg_sq = torch.mean(x**2) 
        
        # Estimate complex power: E[|s|^2] = E[s_real^2 + s_imag^2]
        # If x = [real_parts, imag_parts] and E[real_i^2] approx E[imag_i^2] approx E[x_j^2]
        # then E[|s|^2] is approx 2 * E[x_j^2]
        pwr_complex_estimate = 2 * current_element_avg_sq
        
        # Avoid division by zero or issues with very small power
        if pwr_complex_estimate < 1e-12: # Using a small epsilon
            # print("Warning: Input power to channel is very low, returning unnormalized signal.")
            return x, pwr_complex_estimate 

        scale_factor = torch.sqrt(target_power / pwr_complex_estimate)
        out = x * scale_factor
        return out, pwr_complex_estimate 

    def _add_complex_noise(self, signal_complex: torch.Tensor, snr_db: float, h_coeff: torch.Tensor = None):
        """
        Adds complex Gaussian noise to a complex signal.
        Applies Rayleigh fading if h_coeff is provided and is complex.
        Args:
            signal_complex (torch.Tensor): Complex tensor (B, N_complex_symbols).
            snr_db (float): Signal-to-Noise Ratio in dB.
            h_coeff (torch.Tensor, optional): Complex Rayleigh fading coefficient (scalar or per-batch).
                                             If None or real 1.0, effectively AWGN.
        Returns:
            torch.Tensor: Noisy complex signal (potentially equalized).
        """
        signal_power = torch.mean(torch.abs(signal_complex)**2) # Average power per complex symbol per batch item
        if signal_power < 1e-12: # If signal power is negligible, noise calculation is unstable
            # print("Warning: Signal power is very low in _add_complex_noise. Noise might be disproportionate or zero.")
            # Return signal + zero noise or just signal, depending on desired behavior for zero-power signals
            return signal_complex # Or signal_complex + 0j to ensure complex type

        snr_linear = 10**(snr_db / 10.0)
        noise_power_total = signal_power / snr_linear 
        sigma_n_sq = noise_power_total / 2.0 # Variance for real and imag parts of noise
        
        # Handle potential negative sigma_n_sq due to extremely low signal_power or high SNR
        if sigma_n_sq < 0: sigma_n_sq = torch.tensor(0.0, device=self.device) # Avoid NaN from sqrt
        sigma_n = torch.sqrt(sigma_n_sq)

        noise_real = torch.normal(0.0, sigma_n.item(), size=signal_complex.shape, device=self.device, dtype=signal_complex.real.dtype)
        noise_imag = torch.normal(0.0, sigma_n.item(), size=signal_complex.shape, device=self.device, dtype=signal_complex.imag.dtype)
        complex_noise = noise_real + 1j * noise_imag

        if h_coeff is not None and torch.is_complex(h_coeff) and self.channel_type == 'rayleigh': 
            h_coeff = h_coeff.to(self.device) # Ensure device match
            if h_coeff.numel() == 1: # Scalar h, broadcast
                 received_signal = h_coeff * signal_complex + complex_noise
                 # Equalization: Divide by h_coeff (assuming receiver knows h)
                 received_signal_equalized = received_signal / (h_coeff + 1e-9) # Add epsilon for stability
            elif h_coeff.shape[0] == signal_complex.shape[0] and h_coeff.ndim == signal_complex.ndim: # Per-batch h
                 received_signal = h_coeff * signal_complex + complex_noise
                 received_signal_equalized = received_signal / (h_coeff + 1e-9)
            else: # Shape mismatch for h_coeff
                # print(f"Warning: h_coeff shape {h_coeff.shape} not compatible with signal_complex {signal_complex.shape}. Applying AWGN instead.")
                received_signal_equalized = signal_complex + complex_noise
            return received_signal_equalized
        else: # AWGN or h_coeff is effectively 1.0
            return signal_complex + complex_noise


    def forward(self, features_in: torch.Tensor, 
                h1_rayleigh: torch.Tensor, h2_rayleigh: torch.Tensor, 
                p1_power_factor: float, p2_power_factor: float, # These factors were in original signature but not used
                snr_db: float, avg_pwr_norm: bool = False): # avg_pwr_norm also seems unused by call sites
        """
        Processes features through the channel.
        Args:
            features_in (torch.Tensor): Input features (B, FlatFeatureDim). Real-valued.
            h1_rayleigh, h2_rayleigh: Complex Rayleigh coefficients for path 1 and 2.
                                      Expected to be scalar complex tensors or compatible for broadcasting.
            p1_power_factor, p2_power_factor: Original parameters, currently unused in this logic.
            snr_db (float): SNR in dB.
        Returns:
            Tuple[torch.Tensor, torch.Tensor]: noisy_features_path1, noisy_features_path2 (real-valued)
        """
        
        normalized_features_real, pwr_orig = self._complex_normalize_single_input(features_in, target_power=1.0)
        
        B, L_total = normalized_features_real.shape
        if L_total == 0: # Handle empty feature tensor
            return normalized_features_real, normalized_features_real # Return empty tensors of same shape
        if L_total % 2 != 0:
            raise ValueError(f"Feature dimension {L_total} must be even to split into real/imaginary parts.")
        
        L_complex = L_total // 2
        # Ensure slicing does not go out of bounds if L_complex is 0 (i.e. L_total is 0)
        if L_complex == 0:
             features_complex = torch.zeros(B, 0, dtype=torch.complex64, device=self.device)
        else:
             features_complex = normalized_features_real[:, :L_complex] + 1j * normalized_features_real[:, L_complex:]

        # Apply channel effects for path 1
        h1_to_use = h1_rayleigh if self.channel_type == 'rayleigh' else None
        noisy_complex_path1 = self._add_complex_noise(features_complex, snr_db, h_coeff=h1_to_use)

        # Apply channel effects for path 2
        h2_to_use = h2_rayleigh if self.channel_type == 'rayleigh' else None
        noisy_complex_path2 = self._add_complex_noise(features_complex, snr_db, h_coeff=h2_to_use)

        # Convert back to real tensor [B, FlatRealFeatures]
        if L_complex == 0:
            noisy_features_path1_real = torch.zeros(B, 0, dtype=normalized_features_real.dtype, device=self.device)
            noisy_features_path2_real = torch.zeros(B, 0, dtype=normalized_features_real.dtype, device=self.device)
        else:
            noisy_features_path1_real = torch.cat([torch.real(noisy_complex_path1), torch.imag(noisy_complex_path1)], dim=-1)
            noisy_features_path2_real = torch.cat([torch.real(noisy_complex_path2), torch.imag(noisy_complex_path2)], dim=-1)

        # Denormalize (scale back to original power level)
        # pwr_orig is scalar (average power over batch and features)
        # scale_factor should also be scalar
        if pwr_orig < 1e-12 : # If original power was near zero, scale_factor could be problematic or zero
            scale_factor = torch.tensor(0.0, device=self.device) if pwr_orig == 0 else torch.sqrt(pwr_orig) # Avoid NaN from sqrt(neg) if pwr_orig somehow negative
        else:
            scale_factor = torch.sqrt(pwr_orig / 1.0) # Since normalized to 1.0 target power
        
        final_noisy_path1 = noisy_features_path1_real * scale_factor
        final_noisy_path2 = noisy_features_path2_real * scale_factor
        
        return final_noisy_path1, final_noisy_path2

    def noiseless_forward(self, features_in: torch.Tensor):
        """Passes features through without noise (only normalization if any)."""
        normalized_features, _ = self._complex_normalize_single_input(features_in, target_power=1.0)
        # For consistency with the noisy forward, return two identical copies.
        return normalized_features, normalized_features
