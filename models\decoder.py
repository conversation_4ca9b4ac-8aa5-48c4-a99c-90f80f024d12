# models/decoder.py
import torch
import torch.nn as nn
from timm.models.layers import trunc_normal_

# Import shared components from .modules
from .modules import PatchReverseMerging, SwinTransformerBlock


class BasicLayerDecoder(nn.Module):
    """A basic Swin Transformer layer for one stage in the decoder."""

    def __init__(
        self,
        dim,
        out_dim,
        input_resolution,
        depth,
        num_heads,
        window_size,
        mlp_ratio=4.0,
        qkv_bias=True,
        qk_scale=None,
        norm_layer=nn.LayerNorm,
        upsample_layer_type=None,
        drop_path_rates=None,
    ):
        super().__init__()
        self.dim = dim
        self.out_dim = out_dim
        self.input_resolution = input_resolution
        self.depth = depth
        self.drop_path_rates = (
            drop_path_rates if drop_path_rates is not None else [0.0] * depth
        )

        self.blocks = nn.ModuleList(
            [
                SwinTransformerBlock(
                    dim=dim,
                    input_resolution=input_resolution,
                    num_heads=num_heads,
                    window_size=window_size,
                    shift_size=0 if (i % 2 == 0) else window_size // 2,
                    mlp_ratio=mlp_ratio,
                    qkv_bias=qkv_bias,
                    qk_scale=qk_scale,
                    norm_layer=norm_layer,
                    drop_path=self.drop_path_rates[i],
                )
                for i in range(depth)
            ]
        )

        if upsample_layer_type is not None:
            self.upsample = upsample_layer_type(
                input_resolution, dim=dim, out_dim=out_dim, norm_layer=norm_layer
            )
        else:
            self.upsample = None
            self.projection = (
                nn.Linear(dim, out_dim) if dim != out_dim else nn.Identity()
            )

        for i, blk in enumerate(self.blocks):
            if hasattr(blk, "_set_original_window_shift_size"):
                blk._set_original_window_shift_size(
                    window_size, 0 if (i % 2 == 0) else window_size // 2
                )

    def forward(self, x):
        for blk in self.blocks:
            x = blk(x)

        if self.upsample is not None:
            x_processed = self.upsample(x)
        else:
            x_processed = self.projection(x)
        return x_processed

    def update_resolution(self, H_new, W_new):
        self.input_resolution = (H_new, W_new)
        for blk in self.blocks:
            blk.update_resolution(H_new, W_new)

        if self.upsample is not None:
            self.upsample.input_resolution = (H_new, W_new)


class WITT_Decoder(nn.Module):
    def __init__(
        self,
        img_size=(64, 64),
        embed_dims=(512, 256, 128),
        depths=(4, 4, 2),
        num_heads=(8, 4, 2),
        C=96,
        window_size=2,
        mlp_ratio=4.0,
        qkv_bias=True,
        qk_scale=None,
        norm_layer=nn.LayerNorm,
        patch_norm=True,
        stochastic_depth_prob=0.1,
        use_adaptive_modulation=False,
    ):
        super().__init__()
        self.num_stages = len(depths)
        self.embed_dims = embed_dims
        self.final_out_channels = 3
        self.use_adaptive_modulation = use_adaptive_modulation

        self.input_proj_C = C
        if self.input_proj_C != embed_dims[0]:
            self.input_projection = nn.Linear(self.input_proj_C, embed_dims[0])
        else:
            self.input_projection = nn.Identity()

        self.initial_H = img_size[0] // (2**self.num_stages) if img_size[0] > 0 else 0
        self.initial_W = img_size[1] // (2**self.num_stages) if img_size[1] > 0 else 0
        self.target_H = img_size[0]
        self.target_W = img_size[1]

        dpr = [x.item() for x in torch.linspace(0, stochastic_depth_prob, sum(depths))]

        self.layers = nn.ModuleList()
        current_dim_for_stage_input = embed_dims[0]
        current_resolution_for_stage = (self.initial_H, self.initial_W)
        dpr_offset = 0

        for i_stage in range(self.num_stages):
            upsampler_output_dim = (
                embed_dims[i_stage + 1]
                if (i_stage < self.num_stages - 1)
                else self.final_out_channels
            )

            layer = BasicLayerDecoder(
                dim=current_dim_for_stage_input,
                out_dim=upsampler_output_dim,
                input_resolution=current_resolution_for_stage,
                depth=depths[i_stage],
                num_heads=num_heads[i_stage],
                window_size=window_size,
                mlp_ratio=mlp_ratio,
                qkv_bias=qkv_bias,
                qk_scale=qk_scale,
                norm_layer=norm_layer,
                upsample_layer_type=PatchReverseMerging,
                drop_path_rates=dpr[dpr_offset : dpr_offset + depths[i_stage]],
            )
            self.layers.append(layer)

            current_dim_for_stage_input = upsampler_output_dim
            current_resolution_for_stage = (
                current_resolution_for_stage[0] * 2
                if current_resolution_for_stage[0] > 0
                else 0,
                current_resolution_for_stage[1] * 2
                if current_resolution_for_stage[1] > 0
                else 0,
            )
            dpr_offset += depths[i_stage]

        self.activate_input = nn.ReLU(True)

        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            trunc_normal_(m.weight, std=0.02)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)

    def forward(self, x_feat):
        B, L_feat, C_feat = x_feat.shape

        # Handle cases where initial H*W might be 0, leading to L_feat=0
        if self.initial_H * self.initial_W == 0:
            if L_feat == 0:
                return torch.zeros(
                    B,
                    self.final_out_channels,
                    self.target_H,
                    self.target_W,
                    device=x_feat.device,
                    dtype=x_feat.dtype,
                )
            else:
                # print(f"Warning: Decoder L_feat {L_feat}>0 but initial H*W is 0. This is an inconsistent state.")
                return torch.zeros(
                    B,
                    self.final_out_channels,
                    self.target_H,
                    self.target_W,
                    device=x_feat.device,
                    dtype=x_feat.dtype,
                )

        if L_feat != self.initial_H * self.initial_W:
            # print(f"Warning: Decoder input L_feat {L_feat} does not match expected {self.initial_H * self.initial_W} for initial H,W {self.initial_H, self.initial_W}")
            return torch.zeros(
                B,
                self.final_out_channels,
                self.target_H,
                self.target_W,
                device=x_feat.device,
                dtype=x_feat.dtype,
            )

        x = self.activate_input(x_feat)
        x = self.input_projection(x)

        for layer in self.layers:
            x = layer(x)

        L_out_expected = self.target_H * self.target_W

        if x.shape[1] != L_out_expected:
            if L_out_expected == 0 and x.shape[1] == 0:
                pass
            else:
                # print(f"Warning: Decoder output L_feat {x.shape[1]} does not match expected {L_out_expected} for target H,W {self.target_H, self.target_W}. Input L_feat was {L_feat}")
                return torch.zeros(
                    B,
                    self.final_out_channels,
                    self.target_H,
                    self.target_W,
                    device=x_feat.device,
                    dtype=x_feat.dtype,
                )

        if self.target_H == 0 or self.target_W == 0:
            if x.shape[1] == 0:
                return torch.zeros(
                    B,
                    self.final_out_channels,
                    self.target_H,
                    self.target_W,
                    device=x_feat.device,
                    dtype=x_feat.dtype,
                )
            else:
                # print(f"Error: Trying to view non-empty sequence (L={x.shape[1]}) into zero target H/W ({self.target_H}x{self.target_W})")
                return torch.zeros(
                    B,
                    self.final_out_channels,
                    self.target_H,
                    self.target_W,
                    device=x_feat.device,
                    dtype=x_feat.dtype,
                )

        x_img = (
            x.view(B, self.target_H, self.target_W, self.final_out_channels)
            .permute(0, 3, 1, 2)
            .contiguous()
        )
        return x_img

    def update_resolution(self, H_feat_in, W_feat_in):
        self.initial_H = H_feat_in
        self.initial_W = W_feat_in

        self.target_H = (
            self.initial_H * (2**self.num_stages) if self.initial_H > 0 else 0
        )
        self.target_W = (
            self.initial_W * (2**self.num_stages) if self.initial_W > 0 else 0
        )

        current_resolution_for_stage = (self.initial_H, self.initial_W)
        for i_stage, layer in enumerate(self.layers):
            layer.update_resolution(
                current_resolution_for_stage[0], current_resolution_for_stage[1]
            )
            current_resolution_for_stage = (
                current_resolution_for_stage[0] * 2
                if current_resolution_for_stage[0] > 0
                else 0,
                current_resolution_for_stage[1] * 2
                if current_resolution_for_stage[1] > 0
                else 0,
            )


def create_decoder(**kwargs):
    expected_args = [
        "img_size",
        "embed_dims",
        "depths",
        "num_heads",
        "C",
        "window_size",
        "mlp_ratio",
        "qkv_bias",
        "qk_scale",
        "norm_layer",
        "patch_norm",
        "stochastic_depth_prob",
        "use_adaptive_modulation",
    ]

    decoder_specific_kwargs = {k: v for k, v in kwargs.items() if k in expected_args}

    if "stochastic_depth_prob" not in decoder_specific_kwargs:
        decoder_specific_kwargs["stochastic_depth_prob"] = 0.1
    if "use_adaptive_modulation" not in decoder_specific_kwargs:
        decoder_specific_kwargs["use_adaptive_modulation"] = False

    model = WITT_Decoder(**decoder_specific_kwargs)
    return model
