ONNX模型推理测试报告
==================================================

发送端模型: transmitter_model.onnx
接收端模型: receiver_model.onnx
输入图像1: ./quick_test_results/single_test\sample_image1.png
输入图像2: ./quick_test_results/single_test\sample_image2.png

推理时间:
  发送端: 0.0280秒
  接收端: 0.0827秒
  总计: 0.1107秒

质量指标:
  图像1 - MSE: 0.002225, PSNR: 26.53 dB
  图像2 - MSE: 0.026072, PSNR: 15.84 dB
  平均PSNR: 21.18 dB

输出文件:
  - original_image1.png: 原始图像1
  - original_image2.png: 原始图像2
  - reconstructed_image1.png: 重构图像1
  - reconstructed_image2.png: 重构图像2
  - comparison_image1.png: 对比图像1
  - comparison_image2.png: 对比图像2
