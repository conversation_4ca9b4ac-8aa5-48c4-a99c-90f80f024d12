#!/usr/bin/env python3
"""
最终的批量推理测试脚本

使用现有的ONNX模型进行批量推理测试
"""

import numpy as np
import onnxruntime as ort
import time
import os
import glob
import random
from PIL import Image
import torchvision.transforms as transforms
from tqdm import tqdm
import argparse

def load_images_batch(image_paths, target_size=(64, 64)):
    """批量加载和预处理图像"""
    batch_tensors = []
    batch_images = []
    valid_paths = []
    
    # 预处理变换
    transform = transforms.Compose([
        transforms.Resize(target_size),
        transforms.ToTensor(),  # 转换为[0,1]范围
    ])
    
    for image_path in image_paths:
        try:
            # 加载图像
            image = Image.open(image_path).convert('RGB')
            
            # 应用变换
            tensor = transform(image)  # [3, H, W]
            
            batch_tensors.append(tensor)
            batch_images.append(image)
            valid_paths.append(image_path)
            
        except Exception as e:
            print(f"跳过图像 {image_path}: {e}")
            continue
    
    if len(batch_tensors) == 0:
        return None, None, None
    
    # 堆叠成批次张量 [B, 3, H, W]
    import torch
    batch_tensor = torch.stack(batch_tensors, dim=0)
    
    return batch_tensor.numpy().astype(np.float32), batch_images, valid_paths

def get_image_files_from_directory(image_dir, max_images=None, random_seed=42):
    """从目录中获取图像文件列表"""
    # 支持的图像格式
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff']
    
    # 获取所有图像文件
    image_files = []
    for ext in image_extensions:
        image_files.extend(glob.glob(os.path.join(image_dir, ext)))
        image_files.extend(glob.glob(os.path.join(image_dir, ext.upper())))
    
    if len(image_files) == 0:
        print(f"在目录 {image_dir} 中没有找到图像文件")
        return []
    
    print(f"找到 {len(image_files)} 个图像文件")
    
    # 随机打乱
    if random_seed is not None:
        random.seed(random_seed)
        random.shuffle(image_files)
    
    # 限制数量
    if max_images is not None and max_images < len(image_files):
        image_files = image_files[:max_images]
        print(f"限制为前 {max_images} 个图像文件")
    
    return image_files

def test_batch_inference_simple(image_dir, batch_size=4, max_images=20):
    """简单的批量推理测试"""
    
    # 加载ONNX模型
    print("加载ONNX模型...")
    try:
        transmitter_session = ort.InferenceSession("transmitter_model.onnx")
        receiver_session = ort.InferenceSession("receiver_model.onnx")
        print("成功加载模型")
    except Exception as e:
        print(f"模型加载失败: {e}")
        return False
    
    # 获取图像文件列表
    image_files = get_image_files_from_directory(image_dir, max_images)
    if len(image_files) == 0:
        return False
    
    # 确保图像数量是偶数（因为需要成对处理）
    if len(image_files) % 2 != 0:
        image_files = image_files[:-1]
    
    # 分成两组：image1和image2
    image1_files = image_files[::2]  # 偶数索引
    image2_files = image_files[1::2]  # 奇数索引
    
    total_pairs = len(image1_files)
    print(f"将处理 {total_pairs} 个图像对，批次大小: {batch_size}")
    
    # 统计信息
    total_transmitter_time = 0
    total_receiver_time = 0
    processed_images = 0
    
    # 按批次处理
    num_batches = (total_pairs + batch_size - 1) // batch_size
    
    for batch_idx in tqdm(range(num_batches), desc="处理批次"):
        start_idx = batch_idx * batch_size
        end_idx = min(start_idx + batch_size, total_pairs)
        
        # 获取当前批次的图像文件
        batch_image1_files = image1_files[start_idx:end_idx]
        batch_image2_files = image2_files[start_idx:end_idx]
        
        try:
            # 批量加载图像
            batch1_tensor, batch1_images, valid_paths1 = load_images_batch(batch_image1_files)
            batch2_tensor, batch2_images, valid_paths2 = load_images_batch(batch_image2_files)
            
            if batch1_tensor is None or batch2_tensor is None:
                print(f"跳过批次 {batch_idx}: 图像加载失败")
                continue
            
            # 确保两个批次大小相同
            actual_batch_size = min(batch1_tensor.shape[0], batch2_tensor.shape[0])
            batch1_tensor = batch1_tensor[:actual_batch_size]
            batch2_tensor = batch2_tensor[:actual_batch_size]
            
            print(f"批次 {batch_idx}: 处理 {actual_batch_size} 个图像对")
            
            # 发送端推理
            start_time = time.time()
            transmitter_outputs = transmitter_session.run(
                None, {"input1": batch1_tensor, "input2": batch2_tensor}
            )
            
            interleaved_features = transmitter_outputs[0]
            transmitter_time = time.time() - start_time
            total_transmitter_time += transmitter_time
            
            print(f"  发送端推理: {transmitter_time:.4f}秒, 输出形状: {interleaved_features.shape}")
            
            # 由于维度不匹配，我们逐个处理接收端推理
            receiver_start_time = time.time()
            output1_list = []
            output2_list = []
            
            for i in range(actual_batch_size):
                # 取单个样本
                single_feature = interleaved_features[i:i+1]  # 保持批次维度
                
                try:
                    # 接收端推理（单个样本）
                    receiver_outputs = receiver_session.run(
                        None, {"interleaved_feature": single_feature}
                    )
                    output1, output2 = receiver_outputs
                    output1_list.append(output1)
                    output2_list.append(output2)
                    
                except Exception as e:
                    print(f"    样本 {i} 接收端推理失败: {e}")
                    # 创建零填充的输出
                    output1_list.append(np.zeros((1, 3, 64, 64), dtype=np.float32))
                    output2_list.append(np.zeros((1, 3, 64, 64), dtype=np.float32))
            
            receiver_time = time.time() - receiver_start_time
            total_receiver_time += receiver_time
            
            print(f"  接收端推理: {receiver_time:.4f}秒")
            
            processed_images += actual_batch_size
            
            # 显示当前批次的处理速度
            batch_total_time = transmitter_time + receiver_time
            batch_throughput = actual_batch_size / batch_total_time
            print(f"  批次吞吐量: {batch_throughput:.2f} 图像对/秒")
            
        except Exception as e:
            print(f"处理批次 {batch_idx} 时出错: {e}")
            continue
    
    if processed_images == 0:
        print("没有成功处理任何图像")
        return False
    
    # 计算总体统计信息
    total_time = total_transmitter_time + total_receiver_time
    avg_time_per_image = total_time / processed_images
    throughput = processed_images / total_time
    
    # 打印总结
    print(f"\n批量推理测试完成!")
    print(f"处理的图像数量: {processed_images}")
    print(f"批次大小: {batch_size}")
    print(f"总发送端时间: {total_transmitter_time:.4f}秒")
    print(f"总接收端时间: {total_receiver_time:.4f}秒")
    print(f"总处理时间: {total_time:.4f}秒")
    print(f"平均每图像时间: {avg_time_per_image:.4f}秒")
    print(f"吞吐量: {throughput:.2f} 图像/秒")
    
    return True

def main():
    parser = argparse.ArgumentParser(description="最终的批量推理测试")
    
    parser.add_argument("--image_dir", type=str, required=True, help="图像目录路径")
    parser.add_argument("--batch_size", type=int, default=4, help="批次大小")
    parser.add_argument("--max_images", type=int, default=20, help="最大处理图像数量")
    
    args = parser.parse_args()
    
    print("最终的批量推理测试")
    print("=" * 50)
    
    # 检查模型文件是否存在
    if not (os.path.exists("transmitter_model.onnx") and os.path.exists("receiver_model.onnx")):
        print("ONNX模型文件不存在!")
        print("请先运行 convert_to_onnx.py 生成ONNX模型")
        return 1
    
    # 检查图像目录
    if not os.path.exists(args.image_dir):
        print(f"图像目录不存在: {args.image_dir}")
        return 1
    
    # 执行批量推理测试
    success = test_batch_inference_simple(
        args.image_dir,
        args.batch_size,
        args.max_images
    )
    
    if success:
        print("\n批量测试完成!")
        return 0
    else:
        print("\n批量测试失败!")
        return 1

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
