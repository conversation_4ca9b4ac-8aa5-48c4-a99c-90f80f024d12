ONNX模型批量推理测试报告
============================================================

发送端模型: transmitter_model.onnx
接收端模型: receiver_model.onnx
处理的图像对数量: 3 / 3

推理时间统计:
  平均发送端时间: 0.0435秒
  平均接收端时间: 0.0649秒
  平均总时间: 0.1084秒
  总处理时间: 0.3252秒
  吞吐量: 9.23 图像对/秒

质量指标统计:
  图像1 - 平均MSE: 0.004453 ± 0.000853
  图像1 - 平均PSNR: 23.60 ± 0.88 dB
  图像2 - 平均MSE: 0.003645 ± 0.000565
  图像2 - 平均PSNR: 24.44 ± 0.69 dB
  总体平均PSNR: 24.02 dB

PSNR分布:
  图像1 - 最小值: 22.72 dB
  图像1 - 最大值: 24.81 dB
  图像2 - 最小值: 23.65 dB
  图像2 - 最大值: 25.33 dB

输出文件:
  - grid_originals_1.png: 原始图像1网格
  - grid_reconstructed_1.png: 重构图像1网格
  - grid_originals_2.png: 原始图像2网格
  - grid_reconstructed_2.png: 重构图像2网格
