#!/usr/bin/env python3
"""
快速测试脚本

一键运行ONNX模型的快速验证测试
"""

import argparse
import os
import subprocess
import sys


def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n{'=' * 60}")
    print(f"执行: {description}")
    print(f"命令: {' '.join(cmd)}")
    print(f"{'=' * 60}")

    try:
        result = subprocess.run(
            cmd, check=True, capture_output=True, text=True, timeout=300
        )
        print("成功!")
        if result.stdout:
            print("输出:")
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print("失败!")
        print(f"错误: {e}")
        if e.stderr:
            print("错误输出:")
            print(e.stderr)
        return False
    except subprocess.TimeoutExpired:
        print("超时!")
        return False


def main():
    parser = argparse.ArgumentParser(description="ONNX模型快速测试")

    parser.add_argument(
        "--image_dir",
        type=str,
        default="D:/data/CelebA64/test/images",
        help="图像目录路径",
    )
    parser.add_argument("--num_pairs", type=int, default=10, help="测试的图像对数量")
    parser.add_argument(
        "--output_dir", type=str, default="./quick_test_results", help="输出目录"
    )
    parser.add_argument("--skip_single", action="store_true", help="跳过单图像测试")
    parser.add_argument("--skip_batch", action="store_true", help="跳过批量测试")

    args = parser.parse_args()

    print("🚀 ONNX模型快速测试工具")
    print("=" * 60)

    # 检查ONNX模型是否存在
    if not (
        os.path.exists("transmitter_model.onnx")
        and os.path.exists("receiver_model.onnx")
    ):
        print("ONNX模型文件不存在!")
        print("请先运行以下命令生成ONNX模型:")
        print("python convert_to_onnx.py --model_path your_checkpoint.pth ...")
        return 1

    print("找到ONNX模型文件")

    success_count = 0
    total_tests = 0

    # 测试1: 单图像推理测试
    if not args.skip_single:
        total_tests += 1
        print(f"\n📋 测试 {total_tests}: 单图像推理测试")
        cmd = [
            sys.executable,
            "test_onnx_inference.py",
            "--create_samples",
            "--output_dir",
            f"{args.output_dir}/single_test",
        ]

        if run_command(cmd, "单图像推理测试"):
            success_count += 1
            print("单图像测试通过")
        else:
            print("单图像测试失败")

    # 测试2: 批量推理测试
    if not args.skip_batch and os.path.exists(args.image_dir):
        total_tests += 1
        print(f"\n📋 测试 {total_tests}: 批量推理测试")
        cmd = [
            sys.executable,
            "test_onnx_batch_inference.py",
            "--image_dir",
            args.image_dir,
            "--num_pairs",
            str(args.num_pairs),
            "--output_dir",
            f"{args.output_dir}/batch_test",
        ]

        if run_command(cmd, f"批量推理测试 ({args.num_pairs} 图像对)"):
            success_count += 1
            print("批量测试通过")
        else:
            print("批量测试失败")
    elif not args.skip_batch:
        print(f"\n跳过批量测试: 图像目录不存在 {args.image_dir}")

    # 显示测试总结
    print(f"\n{'=' * 60}")
    print("🎯 测试总结")
    print(f"{'=' * 60}")
    print(f"总测试数: {total_tests}")
    print(f"成功测试: {success_count}")
    print(f"失败测试: {total_tests - success_count}")
    print(
        f"成功率: {success_count / total_tests * 100:.1f}%"
        if total_tests > 0
        else "无测试执行"
    )

    if success_count == total_tests and total_tests > 0:
        print("\n所有测试通过! ONNX模型工作正常")
        print("\n查看结果:")
        print(f"  - {args.output_dir}/single_test/ - 单图像测试结果")
        if os.path.exists(args.image_dir):
            print(f"  - {args.output_dir}/batch_test/ - 批量测试结果")

        # 显示关键指标
        try:
            # 尝试读取批量测试报告
            batch_report = f"{args.output_dir}/batch_test/batch_inference_report.txt"
            if os.path.exists(batch_report):
                print("\n关键性能指标:")
                with open(batch_report, "r", encoding="utf-8") as f:
                    lines = f.readlines()
                    for line in lines:
                        if "总体平均PSNR" in line or "吞吐量" in line:
                            print(f"  - {line.strip()}")
        except:
            pass

        return 0
    else:
        print("\n测试失败! 请检查错误信息并修复问题")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
