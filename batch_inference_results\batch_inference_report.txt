ONNX模型批量推理测试报告
============================================================

发送端模型: transmitter_model.onnx
接收端模型: receiver_model.onnx
处理的图像对数量: 40520 / 40520

推理时间统计:
  平均发送端时间: 0.0439秒
  平均接收端时间: 0.0570秒
  平均总时间: 0.1008秒
  总处理时间: 4086.3797秒
  吞吐量: 9.92 图像对/秒

质量指标统计:
  图像1 - 平均MSE: 0.004081 ± 0.001761
  图像1 - 平均PSNR: 24.24 ± 1.72 dB
  图像2 - 平均MSE: 0.004891 ± 0.002058
  图像2 - 平均PSNR: 23.44 ± 1.69 dB
  总体平均PSNR: 23.84 dB

PSNR分布:
  图像1 - 最小值: 15.72 dB
  图像1 - 最大值: 30.93 dB
  图像2 - 最小值: 15.08 dB
  图像2 - 最大值: 31.20 dB

输出文件:
  - grid_originals_1.png: 原始图像1网格
  - grid_reconstructed_1.png: 重构图像1网格
  - grid_originals_2.png: 原始图像2网格
  - grid_reconstructed_2.png: 重构图像2网格
