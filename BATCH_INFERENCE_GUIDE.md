# 批量推理测试指南

本指南说明如何使用批量推理脚本测试ONNX模型在大量真实图像上的性能表现。

## 功能特性

### 🚀 **核心功能**
- ✅ 批量处理大量图像对
- ✅ 自动从目录读取图像文件
- ✅ 支持多种图像格式（JPG、PNG、BMP等）
- ✅ 随机配对和采样
- ✅ 进度条显示处理状态
- ✅ 详细的统计分析报告
- ✅ 网格对比图像生成
- ✅ 可选的单独图像保存

### 📊 **统计指标**
- **质量指标**: MSE、PSNR（平均值、标准差、最值）
- **性能指标**: 推理时间、吞吐量
- **分布分析**: PSNR分布范围
- **处理统计**: 成功/失败图像对数量

## 使用方法

### 基本用法

```bash
# 基本批量测试
python test_onnx_batch_inference.py --image_dir "D:/data/CelebA64/test/images"

# 限制处理数量
python test_onnx_batch_inference.py \
    --image_dir "D:/data/CelebA64/test/images" \
    --num_pairs 50

# 保存详细结果
python test_onnx_batch_inference.py \
    --image_dir "D:/data/CelebA64/test/images" \
    --num_pairs 20 \
    --save_individual \
    --output_dir ./detailed_results
```

### 高级用法

```bash
# 大规模性能测试
python test_onnx_batch_inference.py \
    --image_dir "D:/data/CelebA64/test/images" \
    --num_pairs 1000 \
    --output_dir ./performance_test \
    --random_seed 123

# 使用自定义模型
python test_onnx_batch_inference.py \
    --transmitter_model ./models/custom_transmitter.onnx \
    --receiver_model ./models/custom_receiver.onnx \
    --image_dir "/path/to/test/images" \
    --num_pairs 100
```

## 参数说明

### 必需参数

| 参数 | 说明 | 示例 |
|------|------|------|
| `--image_dir` | 图像目录路径 | `"D:/data/CelebA64/test/images"` |

### 可选参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--transmitter_model` | str | `transmitter_model.onnx` | 发送端模型路径 |
| `--receiver_model` | str | `receiver_model.onnx` | 接收端模型路径 |
| `--num_pairs` | int | None | 处理的图像对数量（None=全部） |
| `--random_seed` | int | 42 | 随机种子 |
| `--output_dir` | str | `./batch_inference_results` | 输出目录 |
| `--save_individual` | flag | False | 保存每个图像对的详细结果 |
| `--image_size` | int int | `[64, 64]` | 图像尺寸 [高度, 宽度] |

## 输出文件说明

### 核心输出文件

1. **`batch_inference_report.txt`** - 详细统计报告
   - 推理时间统计
   - 质量指标统计
   - PSNR分布信息

2. **网格对比图像**:
   - `grid_originals_1.png` - 原始图像1网格（最多16张）
   - `grid_reconstructed_1.png` - 重构图像1网格
   - `grid_originals_2.png` - 原始图像2网格
   - `grid_reconstructed_2.png` - 重构图像2网格

### 详细输出文件（使用 --save_individual）

3. **`pair_XXX/`** 目录 - 每个图像对的详细结果:
   - `original_1.png` / `original_2.png` - 原始图像
   - `reconstructed_1.png` / `reconstructed_2.png` - 重构图像
   - `comparison_1.png` / `comparison_2.png` - 并排对比图像

## 实际测试示例

### 示例1: 快速质量评估（5个图像对）

```bash
python test_onnx_batch_inference.py \
    --image_dir "D:/data/CelebA64/test/images" \
    --num_pairs 5 \
    --save_individual
```

**测试结果：**
- 处理时间：约0.5秒
- 平均PSNR：23.58 dB
- 吞吐量：10.93 图像对/秒

### 示例2: 中等规模性能测试（50个图像对）

```bash
python test_onnx_batch_inference.py \
    --image_dir "D:/data/CelebA64/test/images" \
    --num_pairs 50 \
    --output_dir ./medium_test
```

**测试结果：**
- 处理时间：约5.5秒
- 平均PSNR：24.06 dB
- 吞吐量：9.04 图像对/秒
- PSNR范围：19.14 - 29.71 dB

### 示例3: 大规模基准测试（1000个图像对）

```bash
python test_onnx_batch_inference.py \
    --image_dir "D:/data/CelebA64/test/images" \
    --num_pairs 1000 \
    --output_dir ./benchmark_test \
    --random_seed 2024
```

## 结果分析

### 质量指标解读

1. **PSNR (Peak Signal-to-Noise Ratio)**
   - **> 25 dB**: 高质量重构
   - **20-25 dB**: 中等质量重构
   - **< 20 dB**: 较低质量重构

2. **MSE (Mean Squared Error)**
   - 越小越好，接近0表示重构质量越高
   - 典型范围：0.001 - 0.01

3. **标准差分析**
   - 标准差小：模型性能稳定
   - 标准差大：模型性能波动较大

### 性能指标分析

1. **推理时间**
   - 发送端时间：编码 + 量化 + 交织
   - 接收端时间：解交织 + 反量化 + 解码
   - 总时间：端到端处理时间

2. **吞吐量**
   - 单位：图像对/秒
   - 反映模型的实时处理能力

## 最佳实践

### 1. 测试策略

```bash
# 第一步：快速验证（5-10个图像对）
python test_onnx_batch_inference.py \
    --image_dir "your/image/dir" \
    --num_pairs 10 \
    --save_individual

# 第二步：中等规模测试（50-100个图像对）
python test_onnx_batch_inference.py \
    --image_dir "your/image/dir" \
    --num_pairs 100

# 第三步：大规模基准测试（1000+个图像对）
python test_onnx_batch_inference.py \
    --image_dir "your/image/dir" \
    --num_pairs 1000 \
    --output_dir ./final_benchmark
```

### 2. 性能优化建议

1. **不保存单独文件**：大规模测试时不使用 `--save_individual`
2. **合理设置数量**：根据可用时间和资源设置 `--num_pairs`
3. **固定随机种子**：使用 `--random_seed` 确保结果可重现

### 3. 结果对比

```bash
# 测试不同模型配置
python test_onnx_batch_inference.py \
    --transmitter_model model_v1_transmitter.onnx \
    --receiver_model model_v1_receiver.onnx \
    --image_dir "test/images" \
    --num_pairs 100 \
    --output_dir ./model_v1_results

python test_onnx_batch_inference.py \
    --transmitter_model model_v2_transmitter.onnx \
    --receiver_model model_v2_receiver.onnx \
    --image_dir "test/images" \
    --num_pairs 100 \
    --output_dir ./model_v2_results
```

## 故障排除

### 常见问题

1. **图像目录为空**
   ```
   没有找到可用的图像对
   ```
   **解决方案**：检查目录路径和图像格式

2. **内存不足**
   ```
   RuntimeError: [ONNXRuntimeError]
   ```
   **解决方案**：减少 `--num_pairs` 数量

3. **处理速度慢**
   - 使用GPU版本的ONNXRuntime
   - 减少图像尺寸
   - 不保存单独文件

### 性能基准

基于CelebA64测试集的典型性能：

| 图像对数量 | 处理时间 | 平均PSNR | 吞吐量 |
|-----------|----------|----------|--------|
| 5 | 0.5秒 | 23.58 dB | 10.93 对/秒 |
| 50 | 5.5秒 | 24.06 dB | 9.04 对/秒 |
| 100 | 11秒 | ~24 dB | ~9 对/秒 |
| 1000 | 110秒 | ~24 dB | ~9 对/秒 |

## 依赖要求

```bash
pip install onnxruntime numpy pillow torch torchvision tqdm
```

批量推理测试是评估ONNX模型在真实数据上性能的重要工具，建议在模型部署前进行充分的批量测试以确保性能满足要求。
