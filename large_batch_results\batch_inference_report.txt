ONNX模型批量推理测试报告
============================================================

发送端模型: transmitter_model.onnx
接收端模型: receiver_model.onnx
处理的图像对数量: 50 / 50

推理时间统计:
  平均发送端时间: 0.0467秒
  平均接收端时间: 0.0639秒
  平均总时间: 0.1106秒
  总处理时间: 5.5298秒
  吞吐量: 9.04 图像对/秒

质量指标统计:
  图像1 - 平均MSE: 0.003928 ± 0.001821
  图像1 - 平均PSNR: 24.51 ± 1.98 dB
  图像2 - 平均MSE: 0.004825 ± 0.002404
  图像2 - 平均PSNR: 23.61 ± 1.91 dB
  总体平均PSNR: 24.06 dB

PSNR分布:
  图像1 - 最小值: 20.18 dB
  图像1 - 最大值: 29.71 dB
  图像2 - 最小值: 19.14 dB
  图像2 - 最大值: 28.34 dB

输出文件:
  - grid_originals_1.png: 原始图像1网格
  - grid_reconstructed_1.png: 重构图像1网格
  - grid_originals_2.png: 原始图像2网格
  - grid_reconstructed_2.png: 重构图像2网格
