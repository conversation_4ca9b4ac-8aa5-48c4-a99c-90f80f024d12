# ONNX模型推理测试指南

本指南说明如何测试ONNX模型的推理效果，并保存原始图片和重构图像进行对比分析。

## 功能特性

### 🎯 **主要功能**
- ✅ 测试ONNX模型的端到端推理
- ✅ 自动生成示例图像或使用自定义图像
- ✅ 保存原始图像和重构图像
- ✅ 创建并排对比图像
- ✅ 计算重构质量指标（MSE、PSNR）
- ✅ 生成详细的推理报告
- ✅ 支持CPU和GPU推理

### 📊 **输出文件**
- `original_image1.png` / `original_image2.png` - 原始输入图像
- `reconstructed_image1.png` / `reconstructed_image2.png` - 重构图像
- `comparison_image1.png` / `comparison_image2.png` - 并排对比图像
- `sample_image1.png` / `sample_image2.png` - 自动生成的示例图像
- `inference_report.txt` - 详细的推理测试报告

## 使用方法

### 方法1: 使用自动生成的示例图像（推荐用于快速测试）

```bash
# 基本测试
python test_onnx_inference.py --create_samples

# 指定输出目录
python test_onnx_inference.py --create_samples --output_dir ./my_test_results

# 自定义图像尺寸
python test_onnx_inference.py --create_samples --image_size 128 128
```

### 方法2: 使用自定义图像

```bash
# 使用指定的图像文件
python test_onnx_inference.py \
    --image1 /path/to/your/image1.jpg \
    --image2 /path/to/your/image2.jpg \
    --output_dir ./custom_test_results

# 使用不同的ONNX模型
python test_onnx_inference.py \
    --transmitter_model ./models/my_transmitter.onnx \
    --receiver_model ./models/my_receiver.onnx \
    --image1 ./images/test1.png \
    --image2 ./images/test2.png
```

### 方法3: 完整的测试流水线

```bash
# 一键完成：模型创建 → ONNX转换 → 推理测试
python test_complete_pipeline.py --keep_models

# 自定义配置的完整测试
python test_complete_pipeline.py \
    --experiment_name my_test \
    --C 256 \
    --quant_code_dim 64 \
    --image_size 32 32 \
    --keep_models
```

## 参数说明

### test_onnx_inference.py 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--transmitter_model` | str | `transmitter_model.onnx` | 发送端ONNX模型路径 |
| `--receiver_model` | str | `receiver_model.onnx` | 接收端ONNX模型路径 |
| `--image1` | str | None | 输入图像1路径 |
| `--image2` | str | None | 输入图像2路径 |
| `--output_dir` | str | `./onnx_inference_results` | 输出目录 |
| `--create_samples` | flag | False | 创建示例图像进行测试 |
| `--image_size` | int int | `[64, 64]` | 图像尺寸 [高度, 宽度] |

### test_complete_pipeline.py 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--experiment_name` | str | `onnx_test` | 实验名称 |
| `--C` | int | 512 | 瓶颈维度 |
| `--quant_code_dim` | int | 128 | 量化码维度 |
| `--image_size` | int int | `[64, 64]` | 图像尺寸 |
| `--keep_models` | flag | False | 保留生成的ONNX模型文件 |
| `--skip_conversion` | flag | False | 跳过ONNX转换（假设模型已存在） |

## 测试结果分析

### 质量指标解读

1. **MSE (Mean Squared Error)**
   - 范围：[0, +∞)
   - 越小越好，0表示完全相同
   - 典型值：0.001-0.1

2. **PSNR (Peak Signal-to-Noise Ratio)**
   - 单位：dB
   - 越大越好
   - 典型值：20-40 dB
   - > 30 dB: 高质量
   - 20-30 dB: 中等质量
   - < 20 dB: 较低质量

### 性能指标

- **推理时间**：包括发送端和接收端的处理时间
- **内存使用**：模型加载和推理过程中的内存占用
- **吞吐量**：每秒处理的图像对数

## 实际测试示例

### 示例1: 快速质量测试

```bash
# 快速测试模型质量
python test_onnx_inference.py --create_samples
```

**预期输出：**
```
ONNX模型推理测试
==================================================
使用示例图像进行测试...
创建示例图像...
加载ONNX模型...
成功加载模型: transmitter_model.onnx
成功加载模型: receiver_model.onnx
执行发送端推理...
发送端推理完成，耗时: 0.024秒
执行接收端推理...
接收端推理完成，耗时: 0.083秒
图像1 - MSE: 0.002225, PSNR: 26.53 dB
图像2 - MSE: 0.026072, PSNR: 15.84 dB
平均PSNR: 21.18 dB
总推理时间: 0.107秒
```

### 示例2: 使用真实图像测试

```bash
# 准备测试图像
mkdir test_images
# 将你的图像复制到 test_images/ 目录

# 运行测试
python test_onnx_inference.py \
    --image1 test_images/photo1.jpg \
    --image2 test_images/photo2.jpg \
    --output_dir ./real_image_test
```

### 示例3: 批量测试不同配置

```bash
# 测试小模型
python test_complete_pipeline.py \
    --experiment_name small_model \
    --C 256 \
    --quant_code_dim 64 \
    --image_size 32 32

# 测试大模型
python test_complete_pipeline.py \
    --experiment_name large_model \
    --C 1024 \
    --quant_code_dim 256 \
    --image_size 128 128
```

## 故障排除

### 常见问题

1. **模型文件不存在**
   ```
   发送端模型文件不存在: transmitter_model.onnx
   ```
   **解决方案**：先运行 `convert_to_onnx.py` 生成ONNX模型

2. **内存不足**
   ```
   RuntimeError: [ONNXRuntimeError] : 2 : INVALID_ARGUMENT
   ```
   **解决方案**：减小图像尺寸或使用更小的模型配置

3. **CUDA不可用警告**
   ```
   UserWarning: Specified provider 'CUDAExecutionProvider' is not in available provider names
   ```
   **解决方案**：这是正常的，会自动回退到CPU推理

4. **图像加载失败**
   ```
   图像加载失败 /path/to/image.jpg
   ```
   **解决方案**：检查图像路径和格式，支持 PNG、JPG、JPEG

### 性能优化建议

1. **使用GPU推理**：安装 `onnxruntime-gpu` 包
2. **批处理**：修改代码支持批量处理多个图像对
3. **模型优化**：使用 ONNX 优化工具减小模型大小
4. **量化**：使用 INT8 量化减少内存使用

## 依赖要求

```bash
pip install onnxruntime  # CPU版本
# 或
pip install onnxruntime-gpu  # GPU版本

pip install pillow numpy torch torchvision
```

## 输出文件说明

测试完成后，输出目录包含以下文件：

```
output_dir/
├── original_image1.png          # 原始输入图像1
├── original_image2.png          # 原始输入图像2
├── reconstructed_image1.png     # 重构图像1
├── reconstructed_image2.png     # 重构图像2
├── comparison_image1.png        # 对比图像1（原始|重构）
├── comparison_image2.png        # 对比图像2（原始|重构）
├── sample_image1.png           # 自动生成的示例图像1
├── sample_image2.png           # 自动生成的示例图像2
└── inference_report.txt        # 详细测试报告
```

推荐首先查看 `comparison_image1.png` 和 `comparison_image2.png` 来直观评估重构质量，然后查看 `inference_report.txt` 了解详细的数值指标。
